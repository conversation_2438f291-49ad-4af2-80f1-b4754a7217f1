﻿using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.Model.Item;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Model.Vendors;
using System;
using System.Collections.Generic;

namespace eProcurementServices.Domain.Model.Requisition
{
    public class SPRDetail
    {
        public SPRDetail() { }
        public SPRDetail(SPRDetailDTO sprDetailDTO)
        {
            if (sprDetailDTO != null)
            {
                this.RequisitionItemId = sprDetailDTO.RequisitionItemId;
                this.ItemDescription = sprDetailDTO.ItemDescription;
                this.UOM = sprDetailDTO.UOM;
                this.HasApproverChangedEstimatedPrice = sprDetailDTO.HasApproverChangedEstimatedPrice;
                this.EstimatedPrice = sprDetailDTO.EstimatedPrice;
                this.Vendor = sprDetailDTO.Vendor;
                this.PartNumber = sprDetailDTO.PartNumber;
                this.ShipToAddress = sprDetailDTO.ShipToAddress;
                this.GeneralLedgerCode = sprDetailDTO.GeneralLedgerCode;
                this.DeliveryMethod = new DeliveryMethodType() { Id = sprDetailDTO.DeliveryMethodTypeId, Description = sprDetailDTO.DeliveryMethodTypeDescription };
                this.AdditionalInformation = sprDetailDTO.AdditionalInformation;
                this.AcquisitionType = sprDetailDTO.AcquisitionType;
                this.EquipmentType = sprDetailDTO.EquipmentType;

                if (sprDetailDTO.FileAttachments != null)
                {
                    this.FileAttachments = new List<FileAttachment>();
                    foreach (var file in sprDetailDTO.FileAttachments)
                    {
                        this.FileAttachments.Add(new FileAttachment(file));
                    }
                }

                this.BudgetNumber = sprDetailDTO.BudgetNumber;
                this.IsTradeIn = sprDetailDTO.IsTradeIn;
                this.TradeInValue = sprDetailDTO.TradeInValue;

                this.SPRTypeId = sprDetailDTO.SPRTypeId;
                this.ParIdentifier = sprDetailDTO.ParIdentifier;
                this.IsAddToParRequest = sprDetailDTO.IsAddToParRequest;
                this.RejectCode = sprDetailDTO.RejectCode;
                this.RejectionComments = sprDetailDTO.RejectionComments;

                this.UOMHasChanged = sprDetailDTO.UOMHasChanged;
                this.DescriptionHasChanged = sprDetailDTO.DescriptionHasChanged;
                this.EstimatedPriceHasChanged = sprDetailDTO.EstimatedPriceHasChanged;
                this.VendorHasChanged = sprDetailDTO.VendorHasChanged;
                this.PartNumberHasChanged = sprDetailDTO.PartNumberHasChanged;
            }
        }

        public int RequisitionItemId { get; set; }

        public string ItemDescription { get; set; }
        public UOM UOM { get; set; }
        public string AcquisitionType { get; set; }
        public string EquipmentType { get; set; }
        public bool HasApproverChangedEstimatedPrice { get; set; }
        public decimal? EstimatedPrice { get; set; } 
        public Vendor Vendor { get; set; }
        public string PartNumber { get; set; }
        public Address ShipToAddress { get; set; }

        public string GeneralLedgerCode { get; set; }
        public string GeneralLedgerCodeString
        {
            get
            {
                return this.GeneralLedgerCode == null ? null : this.GeneralLedgerCode.PadLeft(6, '0');
            }
        }


        public DeliveryMethodType DeliveryMethod { get; set; }
        public string AdditionalInformation { get; set; }

        public List<FileAttachment> FileAttachments { get; set; }
        
        public string BudgetNumber { get; set; }
        public bool IsTradeIn { get; set; }
        public decimal? TradeInValue { get; set; }

        public int SPRTypeId { get; set; }
        public string ParIdentifier { get; set; }

        public bool IsAddToParRequest { get; set; }

        public string RejectCode { get; set; }

        public string RejectionComments { get; set; }

        public SPRType SPRType
        { 
            get
            {
                return new SPRType() { Id = (SPRTypeEnum)Enum.ToObject(typeof(SPRTypeEnum), this.SPRTypeId) };
            }
        }

        public bool UOMHasChanged { get; set; }

        public bool DescriptionHasChanged { get; set; }

        public bool EstimatedPriceHasChanged { get; set; }

        public bool VendorHasChanged { get; set; }

        public bool PartNumberHasChanged { get; set; }
    }
}
