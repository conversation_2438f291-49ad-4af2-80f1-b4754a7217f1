﻿using eProcurementServices.Domain.Model.Item;
using eProcurementServices.Domain.Model.Requisition;
using System.ComponentModel.DataAnnotations;

namespace eProcurementServices.Domain.DTO
{
    /// <summary>
    /// Represents a data transfer object for a Bill Only Review Item, containing details for review and processing.
    /// </summary>
    public class BillOnlyReviewItemDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the BillOnlyReviewItem.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the associated requisition.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the item.
        /// Maximum length is 50 characters.
        /// </summary>
        [StringLength(50)]
        public string ItemId { get; set; }

        /// <summary>
        /// Gets or sets the description of the item.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the main item.
        /// Nullable.
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// Gets or sets the discount applied to the item.
        /// Nullable.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the quantity of the item to be ordered.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the VboHoldItemConversion associated with the item.
        /// </summary>
        public VboHoldItemConversion VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the SPRDetail associated with the item.
        /// </summary>
        public SPRDetail SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the status type ID of the requisition item.
        /// </summary>
        public int RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the Item details from smart item service.
        /// </summary>
        public Item Item { get; set; }

        /// <summary>
        /// Gets or sets the ParItem details from par service.
        /// </summary>
        public ParItem ParItem { get; set; }
    }
}
