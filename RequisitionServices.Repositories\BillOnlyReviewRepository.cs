using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Linq;

namespace RequisitionServices.Repositories
{
    public class BillOnlyReviewRepository : AbstractRepository, IBillOnlyReviewRepository
    {
        private readonly ILog _log = LogManager.GetLogger(typeof(BillOnlyReviewRepository));

        public BillOnlyReviewRepository(EProcurementContext context) : base(context) { }

        public IQueryable<BillOnlyReviewDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                int[] validRequisitionTypes = { 1, 2, 3, 4 };
                int[] requisitionStatusTypes = { 2, 3, 4, 6, 14 };
                var endDate = request.EndDate.Date.AddDays(1).AddTicks(-1);
                var facilityCoiDs = request.Facilities.Select(f => f.COID).ToList();
                var locationIdentifiers = request.Locations ?? new List<string>();
                bool hasLocationFilter = locationIdentifiers.Any();

                var query = context.Requisitions.AsNoTracking().AsQueryable()
                    .Where(r => validRequisitionTypes.Contains(r.RequisitionTypeId)
                        && requisitionStatusTypes.Contains(r.RequisitionStatusTypeId)
                        && DbFunctions.TruncateTime(r.CreateDate) >= DbFunctions.TruncateTime(request.StartDate)
                        && DbFunctions.TruncateTime(r.CreateDate) <= DbFunctions.TruncateTime(endDate)
                        && (string.IsNullOrEmpty(request.PatientId) || r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => cud.PatientId == request.PatientId)))
                        && (!request.ProcedureDate.HasValue || r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => DbFunctions.TruncateTime(cud.ProcedureDate) == DbFunctions.TruncateTime(request.ProcedureDate))))
                        && r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => cud.PatientName.StartsWith(request.PatientLastName)))
                        && r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => cud.Provider.StartsWith(request.ProviderLastName)))
                        && facilityCoiDs.Any(coid => r.LocationIdentifier.Contains(coid))
                    )
                    .Join(context.Users, r => r.CreatedBy, u => u.AccountName, (r, u) => new { r, u })
                    .GroupJoin(context.RequisitionSubmissionTypes,
                        ru => ru.r.RequisitionSubmissionTypeId,
                        rst => rst.Id,
                        (ru, rst) => new { ru.r, ru.u, rst = rst.DefaultIfEmpty() })
                    .SelectMany(x => x.rst.DefaultIfEmpty(), (x, rst) => new { x.r, x.u, rst })
                    .Select(x => new BillOnlyReviewDTO
                    {
                        RequisitionId = x.r.RequisitionId,
                        RequisitionStatusTypeId = x.r.RequisitionStatusTypeId,
                        RequisitionStatusTypeDescription = x.r.RequisitionStatusType.Description,
                        RequisitionTypeId = x.r.RequisitionTypeId,
                        RequisitionTypeDescription = x.r.RequisitionType.Description,
                        PatientId = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.PatientId).FirstOrDefault(),
                        PatientName = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.PatientName).FirstOrDefault(),
                        PoNumbers = x.r.RequisitionItems.Select(ri => ri.PONumber),
                        Provider = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.Provider).FirstOrDefault(),
                        LocationIdentifier = x.r.LocationIdentifier,
                        CreateDate = x.r.CreateDate,
                        CreatedBy = x.r.CreatedBy,
                        IsVendor = x.r.IsVendor,
                        RequisitionSubmissionTypeId = x.r.RequisitionSubmissionTypeId,
                        RequisitionSubmissionTypeDescription = x.rst.Description,
                        RequisitionerFirstName = x.u.FirstName,
                        RequisitionerLastName = x.u.LastName,
                        CountryCode = x.r.CountryCode,
                        ProcedureDate = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.ProcedureDate).FirstOrDefault(),
                        RequisitionItems = x.r.RequisitionItems.Select(ri => new BillOnlyReviewItemDTO
                        {
                            Id = ri.Id,
                            RequisitionId = ri.RequisitionId,
                            ItemId = ri.ItemId,
                            UnitCost = ri.UnitCost,
                            MainItemId = ri.MainItemId,
                            Discount = ri.Discount,
                            QuantityToOrder = ri.QuantityToOrder,
                            RequisitionItemStatusTypeId = ri.RequisitionItemStatusTypeId,
                            SPRDetail = new SPRDetailDTO
                            {
                                EstimatedPrice = ri.SPRDetail.EstimatedPrice
                            },
                            VboHoldItemConversion = ri.VboHoldItemConversion != null
                                ? new VboHoldItemConversionDto
                                {
                                    UnitCost = ri.VboHoldItemConversion.UnitCost
                                }
                                : null
                        })
                    });

                query = query.Where(dto => string.IsNullOrEmpty(request.RequisitionSearch) ||
                    (
                        dto.RequisitionId.ToString().Contains(request.RequisitionSearch) ||
                        (dto.PatientId != null && dto.PatientId.Contains(request.RequisitionSearch)) ||
                        (dto.PatientName != null && dto.PatientName.Contains(request.RequisitionSearch)) ||
                        (dto.Provider != null && dto.Provider.Contains(request.RequisitionSearch)) ||
                        (dto.LocationIdentifier != null && dto.LocationIdentifier.Contains(request.RequisitionSearch)) ||
                        (dto.CreatedBy != null && dto.CreatedBy.Contains(request.RequisitionSearch)) ||
                        (dto.RequisitionStatusTypeDescription != null && dto.RequisitionStatusTypeDescription.Contains(request.RequisitionSearch)) ||
                        (dto.RequisitionerFirstName != null && dto.RequisitionerFirstName.Contains(request.RequisitionSearch)) ||
                        (dto.RequisitionerLastName != null && dto.RequisitionerLastName.Contains(request.RequisitionSearch)) ||
                        ((dto.RequisitionerFirstName ?? "") + " " + (dto.RequisitionerLastName ?? "")).Contains(request.RequisitionSearch) ||
                        ((dto.RequisitionerLastName ?? "") + " " + (dto.RequisitionerFirstName ?? "")).Contains(request.RequisitionSearch) ||
                        (dto.RequisitionSubmissionTypeDescription != null && dto.RequisitionSubmissionTypeDescription.Contains(request.RequisitionSearch)) ||
                        (hasLocationFilter && dto.LocationIdentifier != null && locationIdentifiers.Contains(dto.LocationIdentifier)) ||
                        dto.PoNumbers.Any(po => po.HasValue && po.Value.ToString().Contains(request.RequisitionSearch)))
                );


                switch (request.SortType)
                {
                    case 0:
                        query = query.OrderByDescending(x => x.CreateDate);
                        break;
                    case 1:
                        query = query.OrderBy(x => x.CreateDate);
                        break;
                    case 2:
                        var reqTypesBillOnly = new[] { 1, 2 };
                        query = query.OrderByDescending(x => reqTypesBillOnly.Contains(x.RequisitionSubmissionTypeId))
                                     .ThenBy(x => x.RequisitionId);
                        break;
                    case 3:
                        query = query.OrderBy(x => x.RequisitionStatusTypeId)
                                     .ThenBy(x => x.RequisitionId);
                        break;
                    default:
                        query = query.OrderByDescending(x => x.CreateDate);
                        break;
                }

                return query;
            }
            catch (Exception ex)
            {
                _log.Error($"Error getting bill only review requisition using patientId, requisitionSearch, etc. given in BillOnlyReviewRepository in RequisitionServices API, Method: BillOnlyReviewRepository.GetBillOnlyReviewRequisitions. Search Term: {request?.RequisitionSearch}", ex);
                throw;
            }
        }

        /// <summary>
        /// Query retrieves a collection of BillOnlyReview requisitions and their associated details for printing,
        /// based on the specified filter and selection criteria in the request from the database.
        /// </summary>
        /// <param name="request">
        /// The <see cref="BillOnlyReviewRequest"/> containing parameters used to filter and select the Bill Only Review requisitions to print.
        /// </param>
        /// <returns>
        /// A BillOnlyReview data access object representing the collection of BillOnlyReview requisitions
        /// that match the provided criteria, suitable for further querying or enumeration.
        /// </returns>
        public IQueryable<BillOnlyReviewDAO> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                int[] validRequisitionTypes = { 1, 2, 3, 4 };
                int[] requisitionStatusTypes = { 2, 3, 4, 6, 14 };
                var endDate = request.EndDate.Date.AddDays(1).AddTicks(-1);
                var facilityCoiDs = request.Facilities.Select(f => f.COID);

                var query = context.Requisitions.AsNoTracking()
                    .Where(r => validRequisitionTypes.Contains(r.RequisitionTypeId)
                    && requisitionStatusTypes.Contains(r.RequisitionStatusTypeId)
                    && DbFunctions.TruncateTime(r.CreateDate) >= DbFunctions.TruncateTime(request.StartDate)
                    && DbFunctions.TruncateTime(r.CreateDate) <= DbFunctions.TruncateTime(endDate)
                    && (string.IsNullOrEmpty(request.PatientId) || r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => cud.PatientId == request.PatientId)))
                    && (!request.ProcedureDate.HasValue || r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => DbFunctions.TruncateTime(cud.ProcedureDate) == DbFunctions.TruncateTime(request.ProcedureDate))))
                    && r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => cud.PatientName.StartsWith(request.PatientLastName)))
                    && r.RequisitionItems.Any(ri => ri.ClinicalUseDetails.Any(cud => cud.Provider.StartsWith(request.ProviderLastName)))
                    && facilityCoiDs.Any(coid => r.LocationIdentifier.Contains(coid)))
                    .Join(context.Users, r => r.CreatedBy, u => u.AccountName, (r, u) => new { r, u })
                    .SelectMany( ru => ru.r.RequisitionDigitalSignOff.OrderBy(dso => dso.Id).Take(1).DefaultIfEmpty(),
                        (ru, dso) => new { ru.r, ru.u, dso })
                    .SelectMany(x => context.DigitalSignoffUsers.Where(dsu => x.dso != null && dsu.Id == x.dso.UserId).DefaultIfEmpty(),
                        (x, dsu) => new { x.r, x.u, DigitalSignOff = x.dso, DigitalSignOffUser = dsu })
                    .Select(x => new BillOnlyReviewDAO
                    {
                        RequisitionId = x.r.RequisitionId,
                        RequisitionStatusTypeId = x.r.RequisitionStatusTypeId,
                        RequisitionStatusTypeDescription = x.r.RequisitionStatusType.Description,
                        RequisitionTypeId = x.r.RequisitionTypeId,
                        RequisitionTypeDescription = x.r.RequisitionType.Description,
                        PatientId = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.PatientId).FirstOrDefault(),
                        PatientName = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.PatientName).FirstOrDefault(),
                        Provider = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.Provider).FirstOrDefault(),
                        LocationIdentifier = x.r.LocationIdentifier,
                        CreateDate = x.r.CreateDate,
                        CreatedBy = x.r.CreatedBy,
                        IsVendor = x.r.IsVendor,
                        RequisitionSubmissionTypeId = x.r.RequisitionSubmissionTypeId,
                        RequisitionerFirstName = x.u.FirstName,
                        RequisitionerLastName = x.u.LastName,
                        CountryCode = x.r.CountryCode,
                        ProcedureDate = x.r.RequisitionItems.SelectMany(ri => ri.ClinicalUseDetails).Select(cud => cud.ProcedureDate).FirstOrDefault(),
                        VProBadgeLog = x.r.VProBadgeLog,
                        DigitalSignOff = x.DigitalSignOff,
                        DigitalSignOffUser = x.DigitalSignOffUser,
                        RequisitionItems = x.r.RequisitionItems.Select(ri => new BillOnlyReviewItemDAO
                        {
                            Description = ri.ItemDescription,
                            ItemId = ri.ItemId,
                            UnitCost = ri.UnitCost,
                            MainItemId = ri.MainItemId,
                            Discount = ri.Discount,
                            StatusTypeId = ri.RequisitionItemStatusTypeId,
                            QuantityToOrder = ri.QuantityToOrder,
                            VendorName = ri.VendorName,
                            VendorNumber = ri.VendorId.ToString(),
                            SPRDetail = ri.SPRDetail == null ? null : new SPRDetailDTO
                            {
                                RequisitionItemId = ri.SPRDetail.RequisitionItemId,
                                ItemDescription = ri.SPRDetail.ItemDescription,
                                GeneralLedgerCode = ri.SPRDetail.GeneralLedgerCode,
                                UOM = new UOM { 
                                    Code = ri.SPRDetail.UOMCode,
                                },
                                EstimatedPrice = ri.SPRDetail.EstimatedPrice,
                                Vendor = new Vendor
                                {
                                    Id = ri.SPRDetail.VendorId,
                                    Name = ri.SPRDetail.VendorName
                                },
                                PartNumber = ri.SPRDetail.PartNumber,
                                ParIdentifier = ri.SPRDetail.ParIdentifier,
                            },                            
                            ReOrderNumber = ri.ReOrder,
                            CatalogNumber = ri.CatalogNumber,
                            GL = ri.GeneralLedgerCode,
                            TotalCost = ri.TotalCost,
                            UOMCode = ri.UOMCode,
                            ParLocation = ri.PARLocation,
                            Par = ri.ParIdentifier,
                            Stock = ri.StockIndicator,
                            Lot = ri.ClinicalUseDetails.Select(cud => cud.LotNumber).AsEnumerable(),
                            Serial = ri.ClinicalUseDetails.Select(cud => cud.SerialNumber).AsEnumerable(),
                            VboHoldItemConversion = ri.VboHoldItemConversion == null ? null : new VboHoldItemConversionDto
                            {
                                RequisitionItemId = ri.VboHoldItemConversion.RequisitionItemId,
                                SmartItemNumber = ri.VboHoldItemConversion.SmartItemNumber,
                                UnitCost = ri.VboHoldItemConversion.UnitCost
                            }
                        })
                    });

                switch (request.SortType)
                {
                    case 0:
                        query = query.OrderByDescending(x => x.CreateDate);
                        break;
                    case 1:
                        query = query.OrderBy(x => x.CreateDate);
                        break;
                    case 2:
                        var reqTypesBillOnly = new[] { 1, 2 };
                        query = query.OrderByDescending(x => reqTypesBillOnly.Contains(x.RequisitionSubmissionTypeId))
                                     .ThenBy(x => x.RequisitionId);
                        break;
                    case 3:
                        query = query.OrderBy(x => x.RequisitionStatusTypeId)
                                     .ThenBy(x => x.RequisitionId);
                        break;
                    default:
                        query = query.OrderByDescending(x => x.CreateDate);
                        break;
                }

                return query;
            }
            catch (Exception ex)
            {
                _log.Error("Error getting bill only review requisition using patientId given in BillOnlyReviewRepository in RequisitionServices API, Method: BillOnlyReviewRepository.PrintBillOnlyReviewRequisitions", ex);
                throw;
            }
        }
    }
}
