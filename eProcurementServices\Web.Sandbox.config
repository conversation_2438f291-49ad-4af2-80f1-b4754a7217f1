<?xml version="1.0" encoding="utf-8"?>

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
	  <section name="featureFlags" type="System.Configuration.NameValueSectionHandler" />
  </configSections>

  <appSettings>
    <add key="HomeAPIUrl" value="http://dev-api.healthtrustpg.com/v1/api/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    
    <add key="SecurityAPIUrl" value="http://dev-api.nsa.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SecurityAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="ProfileAPIUrl" value="http://dev-api-profile.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

	<add key="RequisitionAPIUrl" value="https://sbx-api-requisitions.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="CatalogAPIUrl" value="https://sbx-api-catalogservices.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="PurchasingAPIUrl" value="http://sbx-api-purchasing.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	  
    <add key="SwaggerRootUrl" value="https://sbx-api-eprocurement.healthtrustpg.com" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

	<add key="AcceptedCORSOrigins" value="http://local-eprocurement.healthtrustpg.com,https://local-eprocurement.healthtrustpg.com,http://sbx-eprocurement.healthtrustpg.com,https://sbx-eprocurement.healthtrustpg.com,http://sbx-smart.healthtrustpg.com,https://sbx-smart.healthtrustpg.com" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="jwTokenKey" value="A89Gjhf976DSF7hffh3hcne8U832hfdn233900vn" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="AWSBucket" value="Eproc-dev" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="eProcurementRequisitionUrl" value="http://sbx-smart.healthtrustpg.com/Procurement/MyRequisition/{0}" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="punchOutEncryptionTokenKey" value="A89Gjhf976KL;KGF-kkkkkkkkkk09ML,K456098832hfdn233900vn" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="punchOutBrowserFormPostURL" value="http://sbx-api-eprocurement.healthtrustpg.com/PunchOut/ReceivePunchOutOrderMessage?vendorName={0}" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

    <add key="CacheFacilityDeptExpiryTimer" value="3" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="AppInsightsEnabled" value="False" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ikey" value="62a128a8-b833-4357-8914-b8329cdede2e" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="COIDList" value="02723,02750,16251,16254,05225,05228,05229,05230,05233,05260,05261,05433,06590,07845,07847,08452,09391,25070,31767,34222,34242,34293,34296,18176,08752,25745,03263,03265,06659,08158,08159,08162,08165,08621,08756,09478,09492,09796,18340,31003,31052,08939,09231,09720,09721,09722,09723,09724,09725,09726,09727,09728,09752,25327,25377,57000,57002,57003,57004,57005,57006,57007,57008,57009,57010,57011,57012,57013,57015,57503" />
    <add key ="BulkApprovalServiceAuthKey" value="5EC289B2-AB68-4BB5-BD42-0FF837B9A057" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EnvironmentDomainList" value="HCADEV,NONAFFILDEV,CapellaDEV,LPNTDEV,TRIADDEV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="useDevelopmentDomain" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="useQaDomain" value="false" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="securityProtocol" value="https" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="RedisConnectionString" value="dev-redis.healthtrustpg.com:6379" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
	<add key="RedisAuth" value="0bACAMO978MOW8k6aT23" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
	<add key="RedisKeyPrefix" value="SbxProcurement:" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
  </appSettings>

  <log4net xdt:Transform="Replace">
    <appender name="DebugFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="DEBUG" />
      <param name="File" value="E:\Logs\procurementServices\" />
      <param name="MaxSizeRollBackups" value="-1" />
      <param name="RollingStyle" value="Date" />
      <param name="StaticLogFileName" value="false" />
      <param name="DatePattern" value="yyyyMMdd'_Debug.log'" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} (%F:%L) %m%n" />
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="DEBUG" />
        <levelMax value="FATAL" />
      </filter>
    </appender>
    <appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="INFO" />
      <param name="File" value="E:\Logs\procurementServices\" />
      <param name="MaxSizeRollBackups" value="-1" />
      <param name="RollingStyle" value="Date" />
      <param name="StaticLogFileName" value="false" />
      <param name="DatePattern" value="yyyyMMdd'_Info.log'" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n" />
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="INFO" />
        <levelMax value="WARN" />
      </filter>
    </appender>
    <appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="ERROR" />
      <param name="File" value="E:\Logs\procurementServices\" />
      <param name="MaxSizeRollBackups" value="-1" />
      <param name="RollingStyle" value="Date" />
      <param name="StaticLogFileName" value="false" />
      <param name="DatePattern" value="yyyyMMdd'_Error.log'" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n" />
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="ERROR" />
        <levelMax value="FATAL" />
      </filter>
    </appender>
    <root>
      <appender-ref ref="DebugFileAppender" />
      <appender-ref ref="InfoFileAppender" />
      <appender-ref ref="ErrorFileAppender" />
    </root>
  </log4net>
	<featureFlags>
		<add key="FFIsEntryExitEnabled" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="FFIsEntryExitEnabledForStandardRequisition" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="DigitalSignOffIsEnabled" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="FeatureFlagTotalReqAmount" value="false" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="FFVPROBadgeInFE" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="FFVPROBadgeInBE" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="ViraAutomationOn" value="false" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="BORPrintFeatureOn" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
		<add key="AutoRemoveApproverFeatureOn" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
	</featureFlags>
</configuration>