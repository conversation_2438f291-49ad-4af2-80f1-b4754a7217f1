﻿using eProcurementServices.Domain.Model.DigitalSignOff;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Model.Requisition;
using System;
using System.Collections.Generic;

namespace eProcurementServices.Domain.Model.BillOnlyReview
{
    /// <summary>
    /// Represents a Bill Only Review Requisition.
    /// </summary>
    public class BillOnlyReviewRequisition
    {
        /// <summary>
        /// Default constructor.
        /// </summary>
        public BillOnlyReviewRequisition() { }

        /// <summary>
        /// Gets or sets the Requisition ID.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the creator of the requisition.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the Requisition PAR class.
        /// </summary>
        public string RequisitionParClass { get; set; }

        /// <summary>
        /// Gets or sets the country code.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets the comments for the requisition.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is mobile.
        /// </summary>
        public bool IsMobile { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is for a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Submission Type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the list of requisition items.
        /// </summary>
        public List<RequisitionItem> RequisitionItems { get; set; }

        /// <summary>
        /// Gets or sets the date of the requisition.
        /// </summary>
        public DateTime RequisitionDate { get; set; }

        /// <summary>
        /// Gets or sets the user associated with the requisition.
        /// </summary>
        public User User { get; set; }

        /// <summary>
        /// Gets or sets the digital sign-off details for the requisition.
        /// </summary>
        public RequisitionDigitalSignOff RequisitionDigitalSignOff { get; set; }

        /// <summary>
        /// Gets or sets the digital sign-off user details.
        /// </summary>
        public DigitalSignOffUser DigitalSignOffUser { get; set; }

        /// <summary>
        /// Gets or sets the total amount for the requisition.
        /// </summary>
        public decimal? TotalRequisitionAmount { get; set; }
    }
}