﻿using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Repositories;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices.Interface
{
    /// <summary>
    /// Represents the interface for the BillOnlyReviewService.
    /// </summary>
    public interface IBillOnlyReviewService
    {
        /// <summary>
        /// Retrieves a list of BillOnlyReviewDTO objects based on the provided BillOnlyReviewRequest.
        /// </summary>
        /// <param name="request">The BillOnlyReviewRequest object.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of BillOnlyReviewDTO objects.</returns>
        Task<PaginatedBORDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request);

        /// <summary>
        /// Retrieves a list of BillOnlyReviewDetailsDTO objects for printing, based on the provided BillOnlyReviewRequest.
        /// </summary>
        /// <param name="request">The BillOnlyReviewRequest object containing the filter and selection criteria.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of BillOnlyReviewDetailsDTO objects
        /// that match the specified request criteria and are suitable for printing.
        /// </returns>
        Task<List<BillOnlyReviewDetailsDTO>> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request);
    }
}
