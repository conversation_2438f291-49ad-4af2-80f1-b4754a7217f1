﻿using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Interfaces.Services;
using eProcurementServices.Domain.Model.BillOnlyReview;
using eProcurementServices.Filters;
using log4net;
using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace eProcurementServices.Controllers
{
    [TokenAuth]
    public class BillOnlyReviewController : ApiController
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private IBillOnlyReviewService _billOnlyReviewService;

        public BillOnlyReviewController(IBillOnlyReviewService billOnlyReviewService)
            {
            _billOnlyReviewService = billOnlyReviewService;
            }
        /// <summary>
        ///Retrieves bill only review requisitions based on the specified request
        /// </summary>
        /// <param name="request">The request containing search criteria</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PaginatedBORResult> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                request.UserName = HttpContext.Current.User.Identity.Name;
                return await _billOnlyReviewService.GetBillOnlyReviewRequisitions(request);
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error retrieving bill only review requisition using patientId given in BillOnlyReviewController in eProcurementServices API, Method: BillOnlyReviewController.GetBillOnlyReviewRequisitions"), ex);
                throw;
            }
        }

        [HttpPost]
        public async Task<HttpResponseMessage> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                request.UserName = HttpContext.Current.User.Identity.Name; // Still requires System.Web
                byte[] pdfBytes = await _billOnlyReviewService.PrintBillOnlyReviewRequisitions(request);

                // Check if the service returned data (e.g., handled the "No Results" case)
                if (pdfBytes == null || pdfBytes.Length == 0)
                {
                    // Return a 'Not Found' or 'No Content' response
                    return Request.CreateResponse(HttpStatusCode.NotFound, "No data found to generate PDF.");
                    // Or: return Request.CreateResponse(HttpStatusCode.NoContent);
                }

                // Create the response message
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

                // Set the content to the PDF bytes
                response.Content = new ByteArrayContent(pdfBytes);

                // Set the content type header to application/pdf
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");

                // Set the content disposition header to suggest a filename and trigger download
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = $"BillOnlyReview_{DateTime.Now:yyyyMMddHHmmss}.pdf" // Example filename
                                                                                   // FileName = "BillOnlyReviewRequisitions.pdf" // Simpler filename
                };

                return response;
            }
            catch (Exception ex)
            {
                log.Error("Error generating bill only review requisition PDF in BillOnlyReviewController", ex);
                // Return an error response instead of rethrowing (usually better for APIs)
                return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, "An error occurred while generating the PDF.", ex);
                // Or keep the throw if you have global exception handling configured appropriately
                // throw;
            }
        }
    }
}