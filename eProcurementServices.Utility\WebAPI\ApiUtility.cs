﻿using Amazon.ElasticFileSystem.Model;
using eProcurementServices.Utility.Extensions;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace eProcurementServices.Utility.WebAPI
{
    public static class ApiUtility
    {
        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);
        
        /// <summary>
        /// Executes a HTTP GET call to the specified URL/Action
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="customHeaders"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="acceptEmptyParameters">If true, allows constructing empty query string parameters. Defaults to false</param>
        /// <returns></returns>
        public static T ExecuteApiGetTo<T>(string apiUrl, string action, Dictionary<string, string> parameters, string username = null, string password = null, Dictionary<string, string> customHeaders = null, bool acceptEmptyParameters = false)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters, acceptEmptyParameters);
            var response = client.GetAsync(queryUrl).Result;

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                var returnResponse = response.Content.ReadAsAsync<T>();
                return returnResponse.Status == TaskStatus.Faulted ? default(T) : returnResponse.Result;
            }

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
        }
        /// <summary>
        /// Get call that is async
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl"></param>
        /// <param name="action"></param>
        /// <param name="parameters"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <param name="acceptEmptyParameters"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static async Task<T> ExecuteApiGetToAsync<T>(string apiUrl, string action, Dictionary<string, string> parameters, string username = null, string password = null, Dictionary<string, string> customHeaders = null, bool acceptEmptyParameters = false)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters, acceptEmptyParameters);
            var response = await client.GetAsync(queryUrl);

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                var returnResponse = await response.Content.ReadAsAsync<T>();
                return returnResponse;
            }
            else if (response.StatusCode == HttpStatusCode.NotFound)
            {
                Logger.Info($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new KeyNotFoundException($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
            else
            {
                Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
        }

        public static T ExecuteSecurityTokenApiGetTo<T>(string apiUrl, Dictionary<string, string> parameters,
            string token = "", bool acceptEmptyParameters = false)
        {
            var client = ApiFactory.GetSecurityClient(apiUrl);
    
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
                string.IsNullOrWhiteSpace(token) ? HttpContext.Current.Request.Headers["PingAuthorization"] : token.Substring(7));

            Logger.Debug($"Initiating call {apiUrl}");
            var queryUrl = QueryStringBuilder(apiUrl, parameters, acceptEmptyParameters);
            var response = client.GetAsync(queryUrl).Result;

            if (response.IsSuccessStatusCode) return response.Content.ReadAsAsync<T>().Result;

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}. API Response: {response.Content}");
        }
        /// <summary>
        /// Post to security token api
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl"></param>
        /// <param name="parameters"></param>
        /// <param name="token"></param>
        /// <param name="acceptEmptyParameters"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static T ExecuteSecurityTokenApiPostTo<T>(string apiUrl, Dictionary<string, string> parameters,
            string token = "", bool acceptEmptyParameters = false)

        {
            var client = ApiFactory.GetSecurityClient(apiUrl);
            HttpContent httpContent = new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(parameters), Encoding.UTF8, "application/json");

            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
                string.IsNullOrWhiteSpace(token) ? HttpContext.Current.Request.Headers["PingAuthorization"] : token.Substring(7));

            Logger.Debug($"Initiating call {apiUrl}");
            var queryUrl = QueryStringBuilder(apiUrl, parameters, acceptEmptyParameters);
            var response = client.PostAsync(queryUrl, httpContent).Result;

            if (response.IsSuccessStatusCode) return response.Content.ReadAsAsync<T>().Result;

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}. API Response: {response.Content}");
        }

        /// <summary>
        /// Post Security api without query string params
        /// </summary>
        /// <param name="apiUrl"></param>
        /// <param name="parameters"></param>
        /// <param name="token"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static T ExecuteSecurityApiPostTo<T>(string apiUrl, Dictionary<string, object> parameters,
            string token = "")
        {
            var client = ApiFactory.GetSecurityClient(apiUrl);
            HttpContent httpContent = new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(parameters), Encoding.UTF8, "application/json");
            
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
                string.IsNullOrWhiteSpace(token) ? HttpContext.Current.Request.Headers["PingAuthorization"] : token.Substring(7));
            
            Logger.Debug($"Initiating call {apiUrl}");
            var response = client.PostAsync(apiUrl, httpContent).Result;
            
            if (response.IsSuccessStatusCode) return response.Content.ReadAsAsync<T>().Result;
            
            Logger.Error($"{apiUrl} returned {response.StatusCode} with content {response.Content.ReadAsStringAsync().Result}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {apiUrl}. API Response: {response.Content}");
        }

        public static T ExecutePurchasingPingTokenApiGetTo<T>(string apiUrl)
        {
            var client = ApiFactory.GetPurchasingClient(apiUrl);
            
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
                HttpContext.Current.Request.Headers["PingAuthorization"].Substring(7));
            
            Logger.Debug($"Initiating call {apiUrl}");
            var response = client.GetAsync(apiUrl).Result;
            
            if (response.IsSuccessStatusCode) return response.Content.ReadAsAsync<T>().Result;
            
            Logger.Error($"{apiUrl} returned {response.StatusCode} with content {response.Content}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {apiUrl}. API Response: {response.Content}");
        }

        public static T ExecuteApiDeleteTo<T>(string apiUrl, string action, Dictionary<string, string> parameters, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = client.DeleteAsync(queryUrl).Result;

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                var returnResponse = response.Content.ReadAsAsync<T>();
                return returnResponse.Status == TaskStatus.Faulted ? default(T) : returnResponse.Result;
            }

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
        }

        public static async Task<T> ExecuteApiDeleteToAsync<T>(string apiUrl, string action, Dictionary<string, string> parameters, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = await client.DeleteAsync(queryUrl);

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsAsync<T>();
            }
            else if (response.StatusCode == HttpStatusCode.NotFound)
            {
                Logger.Info($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new KeyNotFoundException($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
            else
            {
                Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
        }
        
        /// <summary>
        /// Executes a HTTP GET call to the specified URL/Action using basic auth header from current user
        /// </summary>
        /// <param name="apiUrl"></param>
        /// <param name="action"></param>
        /// <param name="parameters"></param>
        /// <param name="content"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <exception cref="Exception"></exception>
        public static void ExecuteApiPatchTo(string apiUrl, string action, Dictionary<string, string> parameters, HttpContent content, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = client.PatchAsync(queryUrl, content).Result;

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
        }

        public static async Task<T> ExecuteApiPatchToAsync<T>(string apiUrl, string action, Dictionary<string, string> parameters, HttpContent content, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = await client.PatchAsync(queryUrl, content);

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsAsync<T>();
            }
            else if (response.StatusCode == HttpStatusCode.NotFound)
            {
                Logger.Info($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new KeyNotFoundException($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
            else
            {
                Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
        }

        /// <summary>
        /// Execute URL and return result string
        /// </summary>
        /// <param name="apiUrl"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        public static string ExecuteApiGetString(string apiUrl, string content) //currently ONLY used by punchout
        {
            HttpContent httpContent = new StringContent(content, Encoding.UTF8, "application/xml");
            string result;
            var client = ApiFactory.GetRequisitionClient(apiUrl, false);

            var response = client.PostAsync(apiUrl, httpContent).Result;

            if (response.IsSuccessStatusCode)
            {
                result = response.Content.ReadAsStringAsync().Result;
            }
            else
            {
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {apiUrl}");
            }

            return result;
        }

        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given HTTPContent
        /// </summary>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="content">HTTP Content to be posted</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <returns></returns>
        public static void ExecuteApiPostTo(string apiUrl, string action, Dictionary<string, string> parameters, HttpContent content, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = client.PostAsync(queryUrl, content).Result;

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                return;
            }

            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
        }

        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given HTTPContent
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="content">HTTP Content to be posted</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <returns></returns>
        public static T ExecuteApiPostTo<T>(string apiUrl, string action, Dictionary<string, string> parameters, HttpContent content, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = client.PostAsync(queryUrl, content).Result;

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsAsync<T>().Result;
            }

            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
        }

        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given HTTPContent
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="content">HTTP Content to be posted</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <returns></returns>
        public static async Task<T> ExecuteApiPostToAsync<T>(string apiUrl, string action, Dictionary<string, string> parameters, HttpContent content, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);
            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            try
            {
                ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

                Logger.Debug($"Initiating call {apiUrl + action}");
                var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
                var response = await client.PostAsync(queryUrl, content);

                CleanClientHeaders(client, useDefaultCredentials, customHeaders);

                if(response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsAsync<T>();
                }
                else if(response.StatusCode == HttpStatusCode.NotFound)
                {
                var errorContent = await response.Content.ReadAsStringAsync();
                    Logger.Info($"{queryUrl} returned {response.StatusCode} with content {errorContent}");
                    throw new KeyNotFoundException($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl} Response:  {errorContent}");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Logger.Error($"{queryUrl} returned {response.StatusCode} with content {errorContent}");
                    throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl} Response:  {errorContent}");
                }
            }
            catch (HttpRequestException httpEx)
            {
                Logger.Error($"HTTP request error: {httpEx.Message}", httpEx);
                throw;
            }
            catch (Exception ex)
            {
                Logger.Error($"General error: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given an object. The object will be JSON'd for transmission to API.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="objectContent">Object to be posted</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <returns></returns>
        public static T ExecuteApiPostWithContentTo<T>(string apiUrl, string action, Dictionary<string, string> parameters, object objectContent, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetRequisitionClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = client.PostAsJsonAsync(queryUrl, objectContent).Result;
            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsAsync<T>().Result;
            }

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content.ReadAsStringAsync()}{Environment.NewLine}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");

        }
        
        /// <summary>
        /// Returns a URL-encoded query string with a supplied list of parameters
        /// </summary>
        /// <param name="url"></param>
        /// <param name="parameters"></param>
        /// <param name="acceptEmptyParameters">If true, allows constructing empty query string parameters. Defaults to false</param>
        /// <returns></returns>
        private static string QueryStringBuilder(string url, Dictionary<string, string> parameters, bool acceptEmptyParameters = false)
        {
            if (parameters?.Any() != true) return url;

            var query = new StringBuilder();
            foreach (var parameter in parameters)
            {
                var hasValue = !string.IsNullOrWhiteSpace(parameter.Value);
                if (!hasValue && !acceptEmptyParameters) continue;

                if (query.Length > 0) query.Append("&");
                query.Append(HttpUtility.UrlEncode(parameter.Key));
                if (hasValue) query.Append("=").Append(HttpUtility.UrlEncode(parameter.Value));
            }
    
            return query.Length > 0 ? $"{url}?{query}" : url;
        }

        private static void ApplyClientHeaders(HttpClient client, bool useDefaultCredentials, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            if (client == null) return;
            
            if (!useDefaultCredentials)
            {
                var authByteArray = Encoding.ASCII.GetBytes(username + ":" + password);
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("basic", Convert.ToBase64String(authByteArray));
            }

            if (customHeaders == null) return;
            
            foreach (var header in customHeaders.Where(header => !client.DefaultRequestHeaders.Contains(header.Key)))
            {
                client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        private static void CleanClientHeaders(HttpClient client, bool useDefaultCredentials, Dictionary<string, string> customHeaders = null)
        {
            if (client == null)
                return;
        
            if (!useDefaultCredentials)
                client.DefaultRequestHeaders.Remove("Authorization");

            if (customHeaders == null)
                return;

            foreach (var header in customHeaders.Where(header => client.DefaultRequestHeaders.Contains(header.Key)))
                client.DefaultRequestHeaders.Remove(header.Key);
        }
    }
}
