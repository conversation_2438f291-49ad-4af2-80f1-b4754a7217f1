﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Castle.Core" version="4.4.1" targetFramework="net472" />
  <package id="Evolve" version="2.4.0" targetFramework="net472" />
  <package id="JWT" version="1.3.4" targetFramework="net472" />
  <package id="log4net" version="2.0.12" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Versioning" version="4.0.0" targetFramework="net472" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Http" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.8" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net472" />
  <package id="PDFsharp-MigraDoc" version="1.50.5147" targetFramework="net472" />
  <package id="Pipelines.Sockets.Unofficial" version="2.2.8" targetFramework="net472" />
  <package id="Polly" version="5.6.1" targetFramework="net472" />
  <package id="Smart.Core.Common" version="3.4.1" targetFramework="net472" />
  <package id="Smart.Core.Contracts" version="1.2.1" targetFramework="net472" />
  <package id="StackExchange.Redis" version="2.8.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.7.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="5.0.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net472" />
  <package id="System.Threading.Channels" version="5.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="WindowsAzure.Storage" version="9.2.0" targetFramework="net472" />
</packages>