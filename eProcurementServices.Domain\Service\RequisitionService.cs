using Castle.Core.Internal;
using eProcurementServices.Domain.Constants;
using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.DTO.PurchasingDTO;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.Interfaces.Services;
using eProcurementServices.Domain.Interfaces.Utilities;
using eProcurementServices.Domain.Model.FacilityWorkflow;
using eProcurementServices.Domain.Model.Item;
using eProcurementServices.Domain.Model.ItemInfo;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Model.Requisition;
using eProcurementServices.Domain.Model.Vira;
using eProcurementServices.Domain.Utility;
using eProcurementServices.Utility.FileUtility;
using eProcurementServices.Utility.WebAPI;
using log4net;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.BillOnlyReview;
using Smart.Core.Common.Exceptions;
using Smart.Core.Common.Extensions;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace eProcurementServices.Domain.Service
{
    public class RequisitionService : IRequisitionService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private const string saveRequisitionMethod = "Requisition/SaveRequisition/";
        private const string saveRequisitionAsApproverMethod = "Requisition/SaveRequisitionAsApprover/";
        private const string getMyRequisitionsResults = "Requisition/GetMyRequisitionsResults/";
        private const string getMyApprovalsResults = "Requisition/GetMyApprovalsResults/";
        private const string getUpcomingApprovalsForApproverMethod = "Requisition/GetUpcomingApprovalsForApprover/";
        private const string getRequisitionAsApprover = "Requisition/GetRequisitionAsApprover/";
        private const string getRequisitionByIdMethod = "Requisition/GetRequisition/";

        private const string getAcquisitionEquipment = "api/hca3/smartinbound/AcquistionTypes";
        private const string getParticipatingFacilities = "api/hca3/smartinbound/AcquistionTypes";

        private const string getRequisitionByCoidMethod = "Requisition/GetRequisitionByCoid/";
        private const string getRequisitionByLegacyInfoMethod = "Requisition/GetRequisitionByLegacyInfo/";
        private const string getRequisitionForUpdateMethod = "Requisition/GetRequisitionForUpdate/";
        private const string getRequisitionsByCOIDDatesMethod = "Requisition/GetRequisitions/";
        private const string getRequisitionsWithItemStatusesByCOIDDatesMethod = "Requisition/GetRequisitionsWithItemStatuses/";
        private const string getRequisitionsByVendorMethod = "Requisition/GetRequisitionsByVendor/";
        private const string getRequisitionsByVendorReportExportMethod = "Requisition/GetRequisitionsByVendorReportExport/";
        private const string getRequisitionsForReportMethod = "Requisition/GetRequisitionsForReport/";
        private const string getVBORequisitionsForReportMethod = "Requisition/GetVBORequisitionsForReport/";
        private const string getRequisitionsForReportExportMethod = "Requisition/GetRequisitionsForReportExport/";
        private const string getVBORequisitionsForReportExportMethod = "Requisition/GetVBORequisitionsForReportExport/";
        private const string getRequisitionsForReportByItemNumberMethod = "Requisition/GetRequisitionsForReportByItemNumber/";
        private const string getRequisitionsForReportByItemNumberExportMethod = "Requisition/GetRequisitionsForReportByItemNumberExport/";
        private const string updateRequisitionStatusMethod = "Requisition/UpdateRequisitionStatus/";
        private const string getItemWithDetailsMethod = "Item/GetItemWithDetails/";
        private const string getRequisitionForVendorUserMethod = "Requisition/GetRequisitionForVendorUser/";
        private string getVersionedItemWithDetailsMethod(ApiVersion version) { return $"v{version.MajorVersion}/{getItemWithDetailsMethod}"; }
        private const string getMultipleItemsWithDetailsMethod = "Item/GetItemsWithDetails/";
        private const string getRequisitionHistoryMethod = "Requisition/GetRequisitionHistory/";
        private const string submitRequisitionMethod = "Requisition/SubmitRequisition/";
        private const string submitApproversRequisitionMethod = "Requisition/SubmitApproversRequisition/";
        private const string submitRequisitionAvailabilityCheckMethod = "Requisition/SmartLegacySubmissionAvailabilityCheck/";
        private const string getApprover = "User/GetApprover/";
        private const string getDelegateUser = "User/GetDelegateUserByApproverId/";
        private const string deleteAttachment = "Requisition/DeleteAttachment/";
        private const string getInFlightQuantity = "Requisition/GetInFlightQuantity/";
        private const string gpoUserId = "GPOUSER";
        private const string getItemInfoByReorderNbr = "ItemInfo/GetItemInfoByReorderNbr/";
        private const string sssApproverStepLabel = "SSS";
        private const string itemCachePrefix = "ITEM_COID_REQUISITIONID_";
        private const string getRequisitionsForPurchasingReportMethod = "Requisition/GetRequisitionsForPurchasingReport/";
        private const string getAdvancedFiltersForPurchasingMethod = "Requisition/GetAdvancedFiltersForPurchasingReport/";
        private const string _getRequisitionsByPONumber = "Requisition/GetRequisitionAndItemsByPONumber";
        private const string getRequisitionsDetailsForBORPrint = "BillOnlyReview/GetRequisitionsDetailsForBORPrint/";

        private const string getViraItemStatusRecord = "Vira/GetViraItemStatusRecordById/";
        private const string updateViraItemStatusRecord = "Vira/UpdateViraItemStatusRecord/";
        private const string createViraItemStatusRecord = "Vira/CreateViraItemStatusRecord/";
        private string reqAPIEndpoint = ConfigurationManager.AppSettings.Get("RequisitionAPIUrl");
        private string entryExitServiceCentralAPIUrl = ConfigurationManager.AppSettings.Get("EntryExitServiceCentralAPIUrl");
        private string entryExitServiceCentralUser = ConfigurationManager.AppSettings.Get("EntryExitServiceCentralUser");
        private string entryExitServiceCentralPwd = ConfigurationManager.AppSettings.Get("EntryExitServiceCentralPwd");
        private string iClassParNotificationMessage = ConfigurationManager.AppSettings.Get("IClassParNotificationMessage");
        readonly int viraRetryInterval = int.Parse(ConfigurationManager.AppSettings.Get("ViraRetryInterval"));
        readonly NameValueCollection featureflagsection = (NameValueCollection)ConfigurationManager.GetSection("featureFlags");

        private IProfileService profileService;
        private IUserService userService;
        private IFacilityWorkflowService facilityWorkflowService;
        private IAdhocReviewService adhocReviewService;
        private IGetFileUtility GetFileUtility;
        private IViraService _viraService;
        private ICOIDService _coidService;
        private IApproverWorkflowService _approverWorkflowService;

        public RequisitionService(IProfileService profileSvc, IUserService userSvc, IFacilityWorkflowService facilitySvc, IAdhocReviewService adhocReviewService, IGetFileUtility getFileUtility, IViraService viraService, ICOIDService coidService, IApproverWorkflowService approverWorkflowService)
        {
            this.profileService = profileSvc;
            this.userService = userSvc;
            this.facilityWorkflowService = facilitySvc;
            this.adhocReviewService = adhocReviewService;
            this.GetFileUtility = getFileUtility;
            this._viraService = viraService;
            _coidService = coidService;
            _approverWorkflowService = approverWorkflowService;
        }

        public IEnumerable<RequisitionStatusHistory> GetRequisitionHistory(int requisitionId)
        {
            var requisitionHistory = ApiUtility.ExecuteApiGetTo<IEnumerable<RequisitionStatusHistory>>(reqAPIEndpoint, getRequisitionHistoryMethod, new Dictionary<string, string>() { { "requisitionId", requisitionId.ToString() } });

            //Add user
            foreach (var requisitionHistoryRecord in requisitionHistory)
            {
                requisitionHistoryRecord.CreatedByFullName = this.GetUserFullName(requisitionHistoryRecord.CreatedBy);
            }

            return requisitionHistory;
        }

        private Requisition SetRequisitionAsVendorType(Requisition requisition, bool isVendorUser)
        {
            requisition.IsVendor = requisition.IsVendor || isVendorUser;
            return requisition;
        }

        private IEnumerable<int> getVendorAffiliateIds(string username)
        {
            return userService.UserHasVendorPart(username) ? profileService.GetVendorAffils(username).Select(x => x.Id) : null;
        }

        public IEnumerable<RequisitionItem> GetParItems(string userName, int requisitionId, string cOID, string departmentId, string parId, bool isVendorUser = false)
        {
            var parItems = profileService.GetParItems(userName, cOID, departmentId, parId);

            IEnumerable<int> vendorAffiliateIds = getVendorAffiliateIds(userName);

            var requisitionItems = new List<RequisitionItem>();
            if (parItems != null && parItems.Any())
            {
                //For capitated reqs, distribute sub-items as necessary
                if (parItems.First().ParType == ParType.Capitated)
                {
                    var fullCapitateList = new List<ParItem>();

                    var mainParItems = parItems.Where(x => x.Item != null && x.Item.IsCapitated).ToList();

                    if (isVendorUser)
                    {
                        mainParItems = mainParItems.Where(x => vendorAffiliateIds.Any(y => y == x.Item.Vendor.Id)).ToList();
                    }

                    if (mainParItems != null && mainParItems.Any())
                    {
                        //Add main item and matching sub-items (can be duplicated if 2 main items with same vendor)
                        foreach (var mainParItem in mainParItems)
                        {
                            var checkVendor = this.CreateRequisitionItem(requisitionId, RequisitionType.Standard, cOID, departmentId, mainParItem.ItemId.ToString(), parId, userName, false, null);
                            if (mainParItem.Item.Vendor != null)
                            {
                                mainParItem.Item.Vendor.Name = checkVendor.Item.Vendor.Name;
                                mainParItem.Item.Vendor.InvalidVendorFlag = checkVendor.Item.Vendor.InvalidVendorFlag;
                            }
                            fullCapitateList.Add(mainParItem);
                            if (mainParItem.Item.Vendor != null)
                            {
                                var subItems = new List<ParItem>();

                                var smallparItems = parItems.Where(x => x.Item != null && !x.Item.IsCapitated && x.Item.Vendor != null && x.Item.Vendor.Id == mainParItem.Item.Vendor.Id).ToList();
                                if (smallparItems != null)
                                {
                                    foreach (var smallParItem in smallparItems)
                                    {
                                        subItems.Add(smallParItem.CloneMe());
                                    }

                                    subItems.ForEach(i => i.MainItemId = mainParItem.Item.Id);

                                    fullCapitateList.AddRange(subItems);
                                }
                            }
                        }
                    }

                    parItems = fullCapitateList;
                }

                if (parItems != null)
                {
                    parItems.ToList().ForEach(x =>
                    {
                        requisitionItems.Add(new RequisitionItem()
                        {
                            QuantityToOrder = 0,
                            Item = x.Item,
                            ParItem = x,
                            LotSerialPairs = new List<LotSerialPair>() { new LotSerialPair() { ClinicalUseDetailsId = 0 } },
                            AvailableParItems = new List<ParItem> { x }
                        });
                    });
                }
            }

            return requisitionItems;
        }

        public (IEnumerable<RequisitionItem> reqItems, int numFound) GetParItemsWithLastOrderedInfo(ItemSearchCriteria itemSearchCriteria)
        {
            var parItemsDTO = profileService.GetParItemsWithLastOrderedInfo(itemSearchCriteria);

            var requisitionItems = new List<RequisitionItem>();
            if (parItemsDTO?.ParItems?.Count > 0)
            {
                if (parItemsDTO.ParItems.First().ParType == ParType.Capitated)
                {
                    var message = $"{MethodBase.GetCurrentMethod().GetMethodName()} Does not support Capitated PAR {itemSearchCriteria.ParId}: {itemSearchCriteria.COID} {itemSearchCriteria.Department}.";
                    log.Error(message);
                    throw new BadRequestException(message);
                }

                foreach (var parItem in parItemsDTO.ParItems)
                {
                    requisitionItems.Add(new RequisitionItem()
                    {
                        QuantityToOrder = 0,
                        Item = parItem.Item,
                        ParItem = parItem,
                        LotSerialPairs = new List<LotSerialPair>() { new LotSerialPair() { ClinicalUseDetailsId = 0 } },
                        AvailableParItems = new List<ParItem> { parItem }
                    });
                }
            }

            return (requisitionItems, null == parItemsDTO ? 0 : parItemsDTO.NumFound);
        }

        public Requisition GetRequisition(int requisitionId, string username)
        {
            var requisition = new Requisition();

            if (requisitionId == (int)RequisitionCreateTypeEnum.Standard)
            {
                requisition = this.CreateNewRequisition(username);
            }
            else if (requisitionId == (int)RequisitionCreateTypeEnum.Template)
            {
                requisition = this.CreateNewTemplate(username);
            }
            else if (requisitionId == (int)RequisitionCreateTypeEnum.Capital)
            {
                requisition = this.CreateNewCapitalRequisition(username);
            }
            else if (requisitionId == (int)RequisitionCreateTypeEnum.PunchOut)
            {
                requisition = this.CreateNewPunchOutRequisition(username);
            }
            else if (requisitionId == (int)RequisitionCreateTypeEnum.Rush)
            {
                requisition = this.CreateNewRushRequisition(username);
            }
            else
            {
                bool isVendorUser = userService.UserHasVendorPart(username);
                List<int> vendorAffiliateIds = null;
                if (isVendorUser)
                {
                    vendorAffiliateIds = getVendorAffiliateIds(username).ToList();
                    requisition = getRequisitionForVendorUser(requisitionId, username, null, vendorAffiliateIds);

                    if (requisition == null)
                    {
                        // current user not allowed to view this requisition
                        List<string> unauthorizedAccessWarning = new List<string> { "Unauthorized Access" };
                        return new Requisition
                        {
                            Warnings = unauthorizedAccessWarning
                        };
                    }
                }
                else
                {
                    requisition = GetRequisitionFromService(requisitionId, username, null, null, false);
                }

                if (requisition.IsRequisitionEditable && (requisition.RequisitionType == RequisitionType.CapitatedBillOnly || requisition.RequisitionType == RequisitionType.CapitatedBillAndReplace))
                {
                    var items = GetParItems(username, requisition.RequisitionId, requisition.COID, requisition.DepartmentId, requisition.RequisitionParClass).ToList();

                    // NOTICE: GetParItems returns the Capitated PAR items in the correct order. Make sure to keep
                    //         the items in that order because the javascript code in requisitionDetailsController is 
                    //         expecting it to remain in that order.
                    requisition.RequisitionItems = items.Select(x => requisition.RequisitionItems.Any(y => x.ParItem.ItemId == y.ParItem.ItemId
                                                                                                        && x.ParItem.IsMainItem == y.ParItem.IsMainItem
                                                                                                        && x.ParItem.MainItemId == y.ParItem.MainItemId)
                                                                ? requisition.RequisitionItems.First(z => x.ParItem.ItemId == z.ParItem.ItemId
                                                                                                        && x.ParItem.IsMainItem == z.ParItem.IsMainItem
                                                                                                        && x.ParItem.MainItemId == z.ParItem.MainItemId)
                                                                : x).ToList();

                    // if requisition is editable and current user is a vendor user, remove items that no longer fit the user's vendor affiliations
                    if (isVendorUser)
                    {
                        vendorAffiliateIds = vendorAffiliateIds ?? getVendorAffiliateIds(username).ToList();
                        requisition.RequisitionItems = requisition.RequisitionItems.Where(x => vendorAffiliateIds.Any(y => y == (x.SPRDetail?.Vendor?.Id ?? x.Item.Vendor.Id))).ToList();
                    }
                }
                var iClassParItems = requisition.RequisitionItems?.Where(x => x.ParItem?.ParId.Contains("I") == true).ToList();

                if (requisition.IsRequisitionEditable && (requisition.RequisitionStatusType == RequisitionStatusType.Draft || requisition.RequisitionStatusType == RequisitionStatusType.Template) && iClassParItems.Any())
                {
                    if (requisition.Warnings == null && iClassParItems.Any())
                    {
                        requisition.Warnings = new List<string>();
                        requisition.IsSubmitBlocked = true;
                        requisition.Warnings.Add(iClassParNotificationMessage);
                    }
                }
            }
            if (string.IsNullOrEmpty(requisition.COID) || !SmartLegacySubmissionAvailabilityCheck(username, requisition.COID))
            {
                requisition.IsSaveBlocked = true;
            }

            if(Convert.ToBoolean(featureflagsection["FFVPROBadgeInBE"]) && requisition.VProBadgeLog?.BadgeInStatusId == 2)
            {
                requisition = this.GetVProBadgeInStatus(requisition);
            }

            return requisition;
        }

        public EntryExitTypes GetAcquisitionEquipment()
        {
            EntryExitTypes acqEquObj = new EntryExitTypes();
            try
            {
                ICacheManager<Object> cacheManager = new RedisCacheManager<object>();
                ICacheProvider cacheProvider = new CacheProvider(cacheManager);
                acqEquObj = cacheProvider.ProvideAcquisitionEquipment(entryExitServiceCentralAPIUrl, getAcquisitionEquipment, entryExitServiceCentralUser, entryExitServiceCentralPwd);
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error calling AcquisitionType and EquipmentType"), ex);
            }

            return acqEquObj;
        }

        public bool GetParticipatingFacilities(string cOID)
        {
            bool isParticipatingFacility = false;
            try
            {
                //if (ConfigurationManager.AppSettings.Get("FFIsEntryExitEnabled").ToLower() == "true")
                //{
                var featureFlagsSection = ConfigurationManager.GetSection("featureFlags") as System.Collections.Specialized.NameValueCollection;
                if (Convert.ToBoolean(featureFlagsSection["FFIsEntryExitEnabled"]))
                {
                    EntryExitParticipatingFacilities PFacilitiesObj = new EntryExitParticipatingFacilities();
                    ICacheManager<Object> cacheManager = new RedisCacheManager<object>();
                    ICacheProvider cacheProvider = new CacheProvider(cacheManager);
                    PFacilitiesObj = cacheProvider.ProvideParticipatingFacilities(entryExitServiceCentralAPIUrl, getParticipatingFacilities, entryExitServiceCentralUser, entryExitServiceCentralPwd, cOID);

                    isParticipatingFacility = PFacilitiesObj.result.ToList().Any(item => item.Coid == Convert.ToInt32(cOID) && item.Participating == "yes");
                }
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error calling Get Participating facility"), ex);
            }
            return isParticipatingFacility;
        }

        public Requisition GetRequisitionByCoid(int requisitionId, string coid, string username, bool isVendorUser = false)
        {
            return isVendorUser ? getRequisitionForVendorUser(requisitionId, username, coid, getVendorAffiliateIds(username)?.ToList()) : GetRequisitionFromService(requisitionId, username, coid);
        }

        public Requisition GetRequisitionByLegacyInfo(int requisitionId, string coid, string userName, string countryCode, bool isLegacy)
        {
            return GetRequisitionFromService(requisitionId, userName, coid, countryCode, isLegacy);
        }

        public Requisition GetRequisition(RequisitionWithDetailsDTO requsitionWithDetails, string username)
        {
            return ToRequisition(requsitionWithDetails, username, false);
        }

        protected Requisition GetRequisitionFromService(int requisitionId, string userName, string coid = null, string countryCode = null, bool isLegacy = false)
        {
            RequisitionWithDetailsDTO requisitionDto;
            if (coid == null)
            {
                requisitionDto = ApiUtility.ExecuteApiGetTo<RequisitionWithDetailsDTO>(reqAPIEndpoint,
                    getRequisitionByIdMethod, new Dictionary<string, string>()
                    {
                        {"requisitionId", requisitionId.ToString()},
                        {"userName", userName}
                    });
            }
            else if (isLegacy && coid != null)
            {
                requisitionDto = ApiUtility.ExecuteApiGetTo<RequisitionWithDetailsDTO>(reqAPIEndpoint,
                    getRequisitionByLegacyInfoMethod, new Dictionary<string, string>()
                    {
                        {"requisitionId", requisitionId.ToString()},
                        {"coid", coid},
                        {"countryCode", countryCode},
                        {"userName", userName},
                        {"isLegacy", isLegacy.ToString()}
                    });
            }
            else
            {
                requisitionDto = ApiUtility.ExecuteApiGetTo<RequisitionWithDetailsDTO>(reqAPIEndpoint,
                    getRequisitionByCoidMethod, new Dictionary<string, string>()
                    {
                        {"requisitionId", requisitionId.ToString()},
                        {"coid", coid},
                        {"userName", userName}
                    });
            }
            return requisitionDto != null
                ? ToRequisition(requisitionDto, userName, isLegacy)
                : null;
        }

        public Requisition GetRequisitionAsApprover(int requisitionId, string userName)
        {
            RequisitionWithDetailsDTO requisitionDto;

            requisitionDto = ApiUtility.ExecuteApiGetTo<RequisitionWithDetailsDTO>(reqAPIEndpoint,
                getRequisitionAsApprover, new Dictionary<string, string>()
                {
                        {"requisitionId", requisitionId.ToString()},
                        {"userName", userName}
                });

            var requisition = requisitionDto != null ? ToRequisition(requisitionDto, userName, false) : null;


            //var requisition = requisitionDto;

            if (requisition.IsVendor)
            {
                IEnumerable<FacilityWorkflowStep> facilityWorkflowSteps = facilityWorkflowService.Get(requisition.COID, WorkflowTypeEnum.Vendor).Steps;
                requisition = this.DetermineApproveDenyEligibility(requisition, facilityWorkflowSteps, userName);
                requisition = this.DetermineSmartAvailability(requisition, facilityWorkflowSteps, userName, false, requisition.COID);
            }
            else
            {
                IEnumerable<UserWorkflowStep> userWorkflowSteps = userService.GetUserWorkflowSteps(requisition.CreatedBy, requisition.COID, (int)requisition.ApplicableWorkflowType);
                requisition = this.DetermineApproveDenyEligibility(requisition, userWorkflowSteps, userName);
                requisition = this.DetermineSmartAvailability(requisition, userWorkflowSteps, userName, false, requisition.COID);
            }

            if (Convert.ToBoolean(featureflagsection["FFVPROBadgeInBE"]) && requisition.VProBadgeLog?.BadgeInStatusId == 2)
            {
                requisition = this.GetVProBadgeInStatus(requisition);
            }

            return requisition;
        }

        Requisition ToRequisition(RequisitionWithDetailsDTO requisitionDTO, string userName, bool isLegacy)
        {
            string fullName = isLegacy ? GetUserFullNameWithoutDomain(requisitionDTO.CreatedBy) : GetUserFullName(requisitionDTO.CreatedBy);

            var requisition = new Requisition(requisitionDTO, fullName);

            var accessLevel = isLegacy ? RequisitionAccessType.ReadOnly : this.IsValidRequisitionForUser(requisition, userName);
            switch (accessLevel)
            {
                case RequisitionAccessType.AccessDenied:
                    throw new UnauthorizedAccessException("User attempted to access a requisition they do not have access to");

                case RequisitionAccessType.ReadOnly:
                    requisition.IsRequisitionEditable = false;
                    break;
                case RequisitionAccessType.FullAccess:
                    requisition.IsRecallAvailable = true;
                    break;
            }

            requisition.Facility = profileService.GetFacility(requisition.COID, userName);
            requisition.Department = profileService.GetDepartment(userName, requisition.COID, requisition.DepartmentId);

            bool isVendorUser = userService.UserHasVendorPart(userName);
            IEnumerable<int> vendorAffiliateIds = (!isLegacy && requisitionDTO.IsVendor && isVendorUser) ? getVendorAffiliateIds(userName) : null;

            for (int i = 0; i < requisition.RequisitionItems.Count(); i++)
            {
                //If editable, run validation
                if (requisition.IsRequisitionEditable && requisition.RequisitionItems.Any())
                {
                    var isVboRequisitionInFlight = requisition.IsVendor && (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold);
                    requisition.RequisitionItems[i] = this.ValidateRequisitionItem(requisition.RequisitionItems[i], userName, requisition.RequisitionType, requisition.COID, requisition.DepartmentId, requisition.IsVendor, requisition.RequisitionId, requisition.IsMobile, vendorAffiliateIds, isVboRequisitionInFlight);
                }

                if (requisition.RequisitionItems[i].SPRDetail != null)
                {
                    if (requisition.RequisitionItems[i].SPRDetail.FileAttachments.Any())
                    {
                        var factory = GetFileUtility.CreateandReturnObj(requisition.Facility.SmartCountryCode[0]);
                        foreach (var file in requisition.RequisitionItems[i].SPRDetail.FileAttachments)
                        {
                            if (!String.IsNullOrWhiteSpace(file.FileName))
                            {
                                file.FilePath = factory.GetFileLink(file.FileName);
                                var fileUploader = userService.GetUser(file.CreatedBy);
                                if (fileUploader != null)
                                {
                                    file.CreatedBy = fileUploader.FirstName + " " + fileUploader.LastName;
                                }
                            }
                        }
                    }
                }
            }

            if (requisition.RequisitionStatusType == RequisitionStatusType.Submitted && (int)requisition.ApplicableWorkflowType != (int)WorkflowTypeEnum.NotApplicable)
            {
                requisition = DetermineReviewAvailable(requisition, userName);
            }

            return requisition;
        }

        private Requisition DetermineReviewAvailable(Requisition requisition, string userName)
        {
            //Preventing NullReferenceException
            if (requisition.Warnings == null)
            {
                requisition.Warnings = new List<string>();
            }

            var userProfile = userService.GetUserProfile(userName);
            if (userProfile != null)
            {
                requisition.IsReviewAvailable = true;
            }

            return requisition;
        }

        private Requisition DetermineSmartAvailability(Requisition requisition, IEnumerable<UserWorkflowStep> userWorkflowSteps, string userName, bool reqIsApproved, string coid)
        {
            if (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval)
            {
                var approverSteps = userWorkflowSteps.Where(x => x.Approver != null && x.Approver.User != null && x.Approver.User.AccountName.ToLower() == userName.ToLower());

                var approvingAsFinal = approverSteps.Any(x => x.IsFinalStep) && !approverSteps.Any(x => x.Step == requisition.ApprovalStep && !x.IsFinalStep);
                var approvingAsFinalRush = approverSteps.Any(x => x.Step == requisition.ApprovalStep && x.IsFinalRushStep);

                if (reqIsApproved && requisition.IsApproveAvailable && (
                    (requisition.ApplicableWorkflowType != WorkflowTypeEnum.Rush && approvingAsFinal)
                    ||
                    (requisition.ApplicableWorkflowType == WorkflowTypeEnum.Rush && approvingAsFinalRush)
                    ))
                {
                    requisition.IsSubmitToSmartLegacyBlocked = !SmartLegacySubmissionAvailabilityCheck(userName, coid);
                }
            }
            return requisition;
        }

        private Requisition DetermineSmartAvailability(Requisition requisition, IEnumerable<FacilityWorkflowStep> facilityWorkflowSteps, string userName, bool reqIsApproved, string coid)
        {
            if (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold)
            {
                var approverSteps = facilityWorkflowSteps.Where(x => x.Approver != null && x.Approver.User != null && x.Approver.User.AccountName.ToLower() == userName.ToLower());

                if (reqIsApproved && requisition.IsApproveAvailable)
                {
                    requisition.IsSubmitToSmartLegacyBlocked = !SmartLegacySubmissionAvailabilityCheck(userName, coid);
                }
            }
            return requisition;
        }

        public bool SmartLegacySubmissionAvailabilityCheck(string userName, string coid)
        {
            return ApiUtility.ExecuteApiGetTo<bool>(reqAPIEndpoint, submitRequisitionAvailabilityCheckMethod, new Dictionary<string, string>() {
                                                                                                                { "userId", userName },
                                                                                                                { "coid", coid }
                                                                                                            });
        }

        private Requisition DetermineApproveDenyEligibility(Requisition requisition, IEnumerable<UserWorkflowStep> userWorkflowSteps, string userName)
        {
            requisition.IsApproveAvailable = false;
            requisition.IsDenyAvailable = false;
            string amountInsufficientKey = "InsufficientApprovalAmount";

            if (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval && userWorkflowSteps != null && requisition.ApprovalStep != null)
            {
                //Preventing NullReferenceException
                if (requisition.Warnings == null)
                {
                    requisition.Warnings = new List<string>();
                }

                var total = requisition.GetRequisitionTotal();

                var currentSteps = userWorkflowSteps.Where(x => x.Step == requisition.ApprovalStep).ToList();
                if (currentSteps != null && currentSteps.Where(x => x.Approver != null && x.Approver.User != null && x.Approver.User.AccountName.ToLower() == userName.ToLower()).Any())
                {
                    var currentUserStep = currentSteps.Where(x => x.Approver != null && x.Approver.User != null && x.Approver.User.AccountName.ToLower() == userName.ToLower()).First();
                    var currentUserStepMaxApprovalAmountForWorkflowType = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? currentUserStep.Approver.CapitalMaxApprovalAmount : currentUserStep.Approver.MaxApprovalAmount;
                    var isFinalStep = currentSteps.Where(x => x.Approver != null && x.DelegatedByUserId == null && x.IsFinalStep).Any();

                    //User is current approver
                    //If non-rush and is in the final step, see if it's been approved for correct amount
                    if (requisition.ApplicableWorkflowType != WorkflowTypeEnum.Rush && isFinalStep)
                    {
                        if (currentUserStep.Approver.User.AccountName.ToLower() == requisition.CreatedBy.ToLower())
                        {
                            //Has been approved by someone else (for any amount), and is currently in the hands of the original requisitioner, with fully approved amount or they can approve full amount
                            if ((requisition.ApprovedAmount >= total || (!currentUserStep.IsFinalRushStep && currentUserStepMaxApprovalAmountForWorkflowType >= total)))
                            {
                                requisition.IsApproveAvailable = true;
                            }
                            else if (requisition.ApprovedAmount < total && currentUserStepMaxApprovalAmountForWorkflowType < total)
                            {
                                requisition.Warnings.Add(ToolTipMessages.factoryWarningMessage(amountInsufficientKey));
                            }
                        }
                        else
                        {
                            //If already approved for the correct amount, good to approve as final OR User can approve the amount (if not their own requisition and their amount >= total)
                            if (requisition.ApprovedAmount >= total || currentUserStepMaxApprovalAmountForWorkflowType >= total)
                            {
                                requisition.IsApproveAvailable = true;
                            }
                            else if (requisition.ApprovedAmount < total && currentUserStepMaxApprovalAmountForWorkflowType < total)
                            {
                                requisition.Warnings.Add(ToolTipMessages.factoryWarningMessage(amountInsufficientKey));
                            }
                        }
                    }
                    else
                    {
                        if (userWorkflowSteps.Any(x => x.IsFinalStep) || (userWorkflowSteps.Any(x => x.IsFinalRushStep) && requisition.ApplicableWorkflowType == WorkflowTypeEnum.Rush))
                        {
                            requisition.IsApproveAvailable = true;
                        }
                    }

                    requisition.IsDenyAvailable = true;
                }
                else
                {
                    var userFinalApproverStep = userWorkflowSteps.Where(x => x.Approver != null && x.IsFinalStep && x.Approver.User != null && x.Approver.User.AccountName.ToLower() == userName.ToLower()).FirstOrDefault();

                    if (userFinalApproverStep != null && (requisition.ApprovalStep <= userFinalApproverStep.Step))
                    {
                        var userFinalApproverStepMaxApprovalAmountForWorkflowType = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? userFinalApproverStep.Approver.CapitalMaxApprovalAmount : userFinalApproverStep.Approver.MaxApprovalAmount;
                        //User is final approver on this req, they can deny it
                        requisition.IsDenyAvailable = true;

                        //Cannot drop down and approve your own req (only allowed when on your step)
                        //if (requisition.CreatedBy != userFinalApproverStep.Approver.User.AccountName) {

                        //If spend limit met or current final approver user meets spend limit
                        if (requisition.ApprovedAmount >= total || userFinalApproverStepMaxApprovalAmountForWorkflowType >= total)
                        {
                            requisition.IsApproveAvailable = true;
                        }
                        else if (requisition.ApprovedAmount < total && userFinalApproverStepMaxApprovalAmountForWorkflowType < total)
                        {

                            requisition.Warnings.Add(ToolTipMessages.factoryWarningMessage(amountInsufficientKey));
                        }
                    }
                }
            }

            return requisition;
        }

        private Requisition DetermineApproveDenyEligibility(Requisition requisition, IEnumerable<FacilityWorkflowStep> facilityWorkflowSteps, string userName)
        {
            requisition.IsApproveAvailable = false;
            requisition.IsDenyAvailable = false;

            if (facilityWorkflowSteps != null && requisition.ApprovalStep != null)
            {
                //Preventing NullReferenceException
                if (requisition.Warnings == null)
                {
                    requisition.Warnings = new List<string>();
                }

                if (facilityWorkflowSteps.Where(x => x.Approver != null && x.Approver.User != null && x.Approver.User.AccountName.ToLower() == userName.ToLower()).Any())
                {
                    if (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold)
                    {
                        requisition.IsApproveAvailable = true;
                        requisition.IsDenyAvailable = true;
                    }
                }
            }

            return requisition;
        }

        private RequisitionAccessType IsValidRequisitionForUser(Requisition requisition, string userName)
        {
            if (requisition != null)
            {
                if (requisition.CreatedBy == null)
                {
                    return RequisitionAccessType.ReadOnly;
                }
                //If user created req, full access
                if (requisition.CreatedBy.Equals(userName, StringComparison.CurrentCultureIgnoreCase))
                {
                    return RequisitionAccessType.FullAccess;
                }

                //If it's adhocReview
                var adhocReviews = adhocReviewService.GetRequisitionAdhocReviews(requisition.RequisitionId);
                if (adhocReviews.Select(x => x.Reviewer).Contains(userName))
                {
                    return RequisitionAccessType.ReadOnly;
                }

                //Else if user has same facility, allow read only access
                var userFacilities = profileService.GetFacilities(userName);
                if ((userFacilities != null && userFacilities.Where(x => x.COID == requisition.COID).Any())) //User has same COID
                {
                    return RequisitionAccessType.ReadOnly;
                }
            }

            //No access level found
            return RequisitionAccessType.AccessDenied;
        }

        public ApproverDelegateInfoDTO GetDelegateInfoForApprover(string userName)
        {
            var dto = new ApproverDelegateInfoDTO();
            dto.HasDelegatedAuthority = false;

            var approver = ApiUtility.ExecuteApiGetTo<Approver>(reqAPIEndpoint, getApprover, new Dictionary<string, string>() { { "userName", userName } });
            if (approver != null && approver.Delegate != null)
            {
                dto.HasDelegatedAuthority = true;
                var delegateUser = ApiUtility.ExecuteApiGetTo<User>(reqAPIEndpoint, getDelegateUser, new Dictionary<string, string>() { { "id", approver.Delegate.ToString() } });
                if (delegateUser != null)
                {
                    delegateUser.UserProfile = userService.GetUserProfile(delegateUser.AccountName);
                    dto.DelegatedUser = delegateUser;
                }
            }

            return dto;
        }

        // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
        // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
        private List<string> FacilityNameFilterMatching(string filterText, List<Facility> usersSocFacilities)
        {
            return (String.IsNullOrEmpty(filterText)) ? null : usersSocFacilities.Where(x => x.Name.ToLowerInvariant().Contains(filterText.ToLowerInvariant())).Select(x => x.COID).ToList();
        }

        // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
        // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
        private List<string> DepartmentNameFilterMatching(string filterText, List<Department> usersSocDepartments)
        {
            return (String.IsNullOrEmpty(filterText)) ? null : usersSocDepartments.Where(x => x.Description.ToLowerInvariant().Contains(filterText.ToLowerInvariant())).Select(x => $"{x.ZeroPaddedCOID}_{x.Id}").ToList();
        }

        private Dictionary<string, string> DictionaryOfFacilityNames(List<Facility> usersSocFacilities)
        {
            Dictionary<string, string> facilityNames = new Dictionary<string, string>();
            usersSocFacilities.ForEach(x =>
            {
                try
                {
                    facilityNames.Add(x.COID, x.Name);
                }
                catch
                {
                    log.Error($"Error encountered when trying to add COID {x.COID} to Dictionary object");
                    throw;
                }
            });
            return facilityNames;
        }

        private Dictionary<string, string> DictionaryOfDepartmentNames(List<Department> usersSocDepartments)
        {
            Dictionary<string, string> departmentNames = new Dictionary<string, string>();
            usersSocDepartments.ForEach(x =>
            {
                try
                {
                    departmentNames.Add($"{x.ZeroPaddedCOID}_{x.Id}", x.Description);
                }
                catch
                {
                    log.Error($"Error encountered when trying to add department {x.ZeroPaddedCOID}_{x.Id} to Dictionary object");
                    throw;
                }
            });
            return departmentNames;
        }

        private LocationHydratedRequisitionDTO HydrateReqFacilityAndDepartment(LocationHydratedRequisitionDTO req, IDictionary<string, string> facilityNames, IDictionary<string, string> departmentNames)
        {
            if (!String.IsNullOrWhiteSpace(req.LocationIdentifier))
            {
                var locationPieces = req.LocationIdentifier.Split('_');
                if (locationPieces.Length == 2)
                {
                    req.FacilityName = (facilityNames.TryGetValue(locationPieces.First(), out string facility)) ? $"{facility} ({locationPieces.First()})" : $"{locationPieces.First()}";
                    req.DepartmentName = (departmentNames.TryGetValue(req.LocationIdentifier, out string department)) ? $"{department} ({locationPieces.Last()})" : $"{locationPieces.Last()}";
                }
            }

            return req;
        }

        public RequisitionListMultiResultsDTO GetMyRequisitionsResults(RequisitionListMultiRequestDto request)
        {
            if (request.LeftSideRequest == null && request.RightSideRequest == null)
            {
                throw new ArgumentException($"One or both requisition list requests are required, user {request.Username} somehow requested none");
            }

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            if (request.LeftSideRequest != null)
            {
                request.LeftSideRequest.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.LeftSideRequest.FilterText, usersSocFacilities);
                request.LeftSideRequest.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.LeftSideRequest.FilterText, usersSocDepartments);
            }
            if (request.RightSideRequest != null)
            {
                request.RightSideRequest.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.RightSideRequest.FilterText, usersSocFacilities);
                request.RightSideRequest.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.RightSideRequest.FilterText, usersSocDepartments);
            }

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            // Req API call
            var reqListResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionListMultiResultsDTO>(reqAPIEndpoint, getMyRequisitionsResults, null, request.ToJsonContent());

            // Repopulating the returned requisitions with facility/department names
            if (reqListResultsDto.LeftSideResults != null)
            {
                reqListResultsDto.LeftSideResults.Requisitions = reqListResultsDto.LeftSideResults.Requisitions.Select(x => this.HydrateReqFacilityAndDepartment(x, facilityNames, departmentNames)).ToList();
            }

            if (reqListResultsDto.RightSideResults != null)
            {
                reqListResultsDto.RightSideResults.Requisitions = reqListResultsDto.RightSideResults.Requisitions.Select(x => this.HydrateReqFacilityAndDepartment(x, facilityNames, departmentNames)).ToList();
            }

            return reqListResultsDto;
        }

        public ApprovalListMultiResultsDTO GetMyApprovalsResults(RequisitionListMultiRequestDto request)
        {
            if (request.LeftSideRequest == null && request.RightSideRequest == null)
            {
                throw new ArgumentException($"One or both requisition list requests are required, user {request.Username} somehow requested none");
            }

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            if (request.LeftSideRequest != null)
            {
                request.LeftSideRequest.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.LeftSideRequest.FilterText, usersSocFacilities);
                request.LeftSideRequest.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.LeftSideRequest.FilterText, usersSocDepartments);
            }
            if (request.RightSideRequest != null)
            {
                request.RightSideRequest.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.RightSideRequest.FilterText, usersSocFacilities);
                request.RightSideRequest.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.RightSideRequest.FilterText, usersSocDepartments);
            }

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            // Req API call
            var approvalListResultsDto = ApiUtility.ExecuteApiPostTo<ApprovalListMultiResultsDTO>(reqAPIEndpoint, getMyApprovalsResults, null, request.ToJsonContent());

            // Repopulating the returned requisitions with facility/department names
            if (approvalListResultsDto.LeftSideResults != null)
            {
                approvalListResultsDto.LeftSideResults.ApprovalDTOs.ForEach(x => x.RequisitionDTO = this.HydrateReqFacilityAndDepartment(x.RequisitionDTO, facilityNames, departmentNames));
            }

            if (approvalListResultsDto.RightSideResults != null)
            {
                approvalListResultsDto.RightSideResults.ApprovalDTOs.ForEach(x => x.RequisitionDTO = this.HydrateReqFacilityAndDepartment(x.RequisitionDTO, facilityNames, departmentNames));
            }

            return approvalListResultsDto;
        }

        public ApprovalListResultsDTO GetUpcomingApprovalsForApprover(RequisitionListRequestDto request)
        {
            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            var ApprovalListResultsDto = ApiUtility.ExecuteApiPostTo<ApprovalListResultsDTO>(reqAPIEndpoint, getUpcomingApprovalsForApproverMethod, null, request.ToJsonContent());

            ApprovalListResultsDto.ApprovalDTOs.ForEach(x => x.RequisitionDTO = this.HydrateReqFacilityAndDepartment(x.RequisitionDTO, facilityNames, departmentNames));

            return ApprovalListResultsDto;
        }

        private RequisitionItem SetRequisitionItemInvalid(RequisitionItem requisitionItem, RequisitionItemStatusType status)
        {
            requisitionItem.IsInvalid = true;
            requisitionItem.RequisitionItemStatusType = status;
            return requisitionItem;
        }

        public RequisitionItem CreateRequisitionItem(int requisitionId, RequisitionType requisitionType, string cOID, string departmentId, string itemId, string parId, string userName, bool isVendorUser, ApiVersion version = null)
        {
            int intItemId;
            var requisitionItem = new RequisitionItem()
            {
                Item = new Item() { Id = itemId },
                QuantityToOrder = 0
            };

            if (!int.TryParse(itemId, out intItemId))
            {
                requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.ItemNotFound);
            }
            else
            {
                //Get item from Req service
                ItemDetailsDTO itemDetails = null;
                //IINItemRecordDTO IINItem = null;
                if (requisitionType != RequisitionType.Capital)
                {
                    var method = version != null ? getVersionedItemWithDetailsMethod(version) : getItemWithDetailsMethod;
                    itemDetails = ApiUtility.ExecuteApiPostWithContentTo<ItemDetailsDTO>(reqAPIEndpoint, method, new Dictionary<string, string>()
                    {
                        { "userName", userName },
                        { "COID", cOID },
                        { "departmentId", departmentId.ToString() }
                    }, new ItemParDTO() { ItemId = itemId, ParId = parId });
                }

                if(itemDetails != null)
                    itemDetails.AvailableParItems = itemDetails.AvailableParItems?.Where(par => !par.ParId.ToLower().StartsWith("i")).ToList();

                IEnumerable<int> vendorAffiliateIds = getVendorAffiliateIds(userName);

                bool vendorAffiliateAllowed = isVendorUser ? itemDetails?.Item?.Vendor != null && vendorAffiliateIds.Any(x => x == itemDetails.Item.Vendor.Id) : true;

                if (!vendorAffiliateAllowed || itemDetails == null || itemDetails.Item == null || String.IsNullOrWhiteSpace(itemDetails.Item.Id))
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.ItemNotFound);
                }
                else
                {
                    requisitionItem.IsFileItem = true;
                    itemDetails.Item.ActualId = itemDetails.Item.Id;
                    requisitionItem.IsInvalid = false;
                    requisitionItem.RequisitionItemStatusType = RequisitionItemStatusType.Valid;
                    requisitionItem.Item = itemDetails.Item;


                    if (requisitionType != RequisitionType.Standard && requisitionType != RequisitionType.Rush)
                    {
                        //Only use BillOnly PARs
                        if (requisitionType == RequisitionType.BillOnly || requisitionType == RequisitionType.BillAndReplace)
                        {
                            requisitionItem.AvailableParItems = itemDetails.AvailableParItems != null ? itemDetails.AvailableParItems.Where(x => x.ParType == ParType.BillOnly && x.ParId == parId).ToList() : null;
                        }
                        //Only allow where ParType = the same par
                        else
                        {
                            requisitionItem.AvailableParItems = itemDetails.AvailableParItems != null ? itemDetails.AvailableParItems.Where(x => x.ParId == parId).ToList() : null;
                        }
                    }
                    else
                    {
                        requisitionItem.AvailableParItems = itemDetails.AvailableParItems != null ? itemDetails.AvailableParItems.Where(x => x.ParType == ParType.Normal || x.ParType == ParType.Pharmacy).ToList() : null;
                    }

                    if (requisitionItem.AvailableParItems != null && requisitionItem.AvailableParItems.Any())
                    {
                        if (String.IsNullOrWhiteSpace(parId))
                        {
                            requisitionItem.ParItem = requisitionItem.AvailableParItems.FirstOrDefault();
                        }
                        else
                        {
                            var parToSelect = requisitionItem.AvailableParItems.Where(x => x.ParId == parId).FirstOrDefault();
                            if (parToSelect != null)
                            {
                                requisitionItem.ParItem = parToSelect;
                            }
                            else
                            {
                                requisitionItem.ParItem = requisitionItem.AvailableParItems.FirstOrDefault();
                            }
                        }
                    }
                    else if (!(isVendorUser && string.Equals(requisitionItem.Item.ComplianceCode, "C", StringComparison.InvariantCultureIgnoreCase)))
                    {
                        requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.NoParsAvailable);
                    }
                }
            }
            if (requisitionType == RequisitionType.BillAndReplace || requisitionType == RequisitionType.BillOnly || requisitionType == RequisitionType.CapitatedBillAndReplace || requisitionType == RequisitionType.CapitatedBillOnly)
            {
                if (requisitionItem.LotSerialPairs == null || !requisitionItem.LotSerialPairs.Any())
                {
                    requisitionItem.LotSerialPairs = new List<LotSerialPair>() { new LotSerialPair() { ClinicalUseDetailsId = 0 } };
                }
            }
            return requisitionItem;
        }

        public bool IsCapitatedReqHaveAnyMainitems(Requisition requisition)
        {
            if (requisition != null && (requisition.RequisitionType == RequisitionType.CapitatedBillAndReplace || requisition.RequisitionType == RequisitionType.CapitatedBillOnly))
            {
                if (requisition.RequisitionItems == null || !requisition.RequisitionItems.Any())
                {
                    return true;
                }
            }

            return false;
        }

        public bool IsCapitatedReqHaveSubitemsForEachMainItems(Requisition requisition)
        {
            if (requisition != null && (requisition.RequisitionType == RequisitionType.CapitatedBillAndReplace || requisition.RequisitionType == RequisitionType.CapitatedBillOnly))
            {
                if (requisition.RequisitionItems == null)
                {
                    return true;
                }
                var mainItems = requisition.RequisitionItems.Where(x => x.RequisitionItemStatusType == RequisitionItemStatusType.Valid && x.QuantityToOrder > 0 && x.ParItem != null && x.ParItem.MainItemId == null).ToList();
                if (mainItems != null)
                {
                    foreach (var mainItem in mainItems)
                    {
                        var subitems = requisition.RequisitionItems.Where(x => x.RequisitionItemStatusType == RequisitionItemStatusType.Valid && x.QuantityToOrder > 0 && x.ParItem.MainItemId == mainItem.Item.Id).ToList();

                        if (!subitems.Any())
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        public bool IsRushReqConvertToStandardReq(Requisition requisition)
        {
            if (requisition != null && requisition.RequisitionType == RequisitionType.Rush)
            {
                if (requisition.RequisitionItems != null)
                {
                    foreach (var reqItem in requisition.RequisitionItems)
                    {
                        if (reqItem.IsRushOrder)
                        {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        public bool HasParItemsOnly(Requisition requisition)
        {
            if (requisition != null)
            {
                if (requisition.RequisitionType == RequisitionType.Capital)
                {
                    return false;
                }
                else if (requisition.RequisitionItems != null)
                {
                    foreach (var reqItem in requisition.RequisitionItems)
                    {
                        //Check for any SPR items
                        if (reqItem.IsSPR)
                        {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        public WorkflowValidationDTO ValidateWorkflowForRequisition(string username, WorkflowTypeEnum workflowType, string coid, bool isVendorRequisition, decimal? requisitionTotal = null)
        {
            var hierarchy = _coidService.GetHierarchyForCoid(coid);
            WorkflowValidationDTO validateWorkflow;
            if (isVendorRequisition)
            {
                var facilityWorkflowSteps = facilityWorkflowService.Get(coid, WorkflowTypeEnum.Vendor);

                var saveFacilityWorkflowDtoRequest = new SaveFacilityWorkflowDTO
                {
                    Workflow = facilityWorkflowSteps
                };
                validateWorkflow = Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true ?
                    facilityWorkflowService.ValidateAndSaveFacilityWorkflow(saveFacilityWorkflowDtoRequest) :
                    facilityWorkflowService.Validate(facilityWorkflowSteps);
            }
            else
            {
                validateWorkflow = Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true ?
                    _approverWorkflowService.ValidateAndSaveUserWorkflowSteps(hierarchy, username, workflowType, coid, requisitionTotal) :
                    userService.ValidateUserWorkflow(username, workflowType, coid, requisitionTotal);
            }
            return validateWorkflow;
        }

        public Requisition ValidateRequisition(Requisition requisition, string userName, bool isVendorUser)
        {
            if (requisition == null)
            {
                return requisition;
            }

            if (requisition.Warnings == null)
            {
                requisition.Warnings = new List<string>();
            }

            if (requisition.Department != null && requisition.Department.IsActive == false)
            {
                requisition.IsSubmitBlocked = true;
                requisition.Warnings.Add("The department is not active. Please use another department or contact your administrator.");
            }

            //Capital requisitions
            if (requisition.RequisitionType == RequisitionType.Capital && requisition.RequisitionItems != null && requisition.RequisitionItems.Any())
            {
                //Check for vendors on Capital Reqs
                if (requisition.RequisitionItems.Where(x => x.SPRDetail != null && x.SPRDetail.Vendor == null).Any())
                {
                    requisition.IsSubmitBlocked = true;
                    requisition.Warnings.Add("Please enter a valid vendor for this capital requisition prior to submitting.");
                }

                //Project # is required
                if (requisition.RequisitionItems.Where(x => x.SPRDetail != null && String.IsNullOrWhiteSpace(x.SPRDetail.BudgetNumber)).Any())
                {
                    requisition.IsSubmitBlocked = true;
                    requisition.Warnings.Add("Project number is required to submit.");
                }

                //Check for vendors on Capital Reqs
                if (requisition.RequisitionItems.Where(x => x.SPRDetail != null && x.SPRDetail.IsTradeIn != false && x.SPRDetail.TradeInValue == null).Any())
                {
                    requisition.IsSubmitBlocked = true;
                    requisition.Warnings.Add("Trade-In Value is required if Trade-In is selected.");
                }
            }

            if (requisition.IsVendor)
            {
                var requisitionTotal = requisition.GetRequisitionTotal();
                var facilityWorkflowSteps = facilityWorkflowService.Get(requisition.COID, WorkflowTypeEnum.Vendor);
                var saveFacilityWorkflowDtoRequest = new SaveFacilityWorkflowDTO
                {
                    Workflow = facilityWorkflowSteps
                };
                //Validate workflow when required
                WorkflowValidationDTO returnDTO = Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true ? facilityWorkflowService.ValidateAndSaveFacilityWorkflow(saveFacilityWorkflowDtoRequest) : facilityWorkflowService.Validate(facilityWorkflowSteps);

                if (returnDTO != null && !returnDTO.IsValid)
                {
                    requisition.IsSubmitBlocked = true;
                    requisition.Warnings.Add("Workflow adjustments required, please contact an Administrator.");
                    if (returnDTO.WarningMessages != null && returnDTO.WarningMessages.Any())
                    {
                        requisition.Warnings.AddRange(returnDTO.WarningMessages);
                    }
                }
            }
            //Check for Rush Items in Rush requisition
            else if (requisition.RequisitionType == RequisitionType.Rush && this.IsRushReqConvertToStandardReq(requisition))
            {
                requisition.Warnings.Add("No Rush item(s) exist on this requisition. It will be converted to Standard on submit.");

                var requisitionTotal = requisition.GetRequisitionTotal();
                var hierarchy = _coidService.GetHierarchyForCoid(requisition.COID);
                var hasStdWorkflow = Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true ?
                    _approverWorkflowService.ValidateAndSaveUserWorkflowSteps(hierarchy, userName, WorkflowTypeEnum.Standard, requisition.COID, requisitionTotal) :
                    userService.ValidateUserWorkflow(userName, WorkflowTypeEnum.Standard, requisition.COID, requisitionTotal);

                var hasParItemsOnly = this.HasParItemsOnly(requisition);

                if (hasStdWorkflow != null && !hasStdWorkflow.IsValid && !hasParItemsOnly)
                {
                    requisition.Warnings.Add("Standard Workflow is not valid.");
                    requisition.Warnings.Add("Workflow adjustments required, please contact an Administrator.");
                    if (hasStdWorkflow.WarningMessages != null && hasStdWorkflow.WarningMessages.Any())
                    {
                        requisition.Warnings.AddRange(hasStdWorkflow.WarningMessages);
                    }
                    requisition.IsSubmitBlocked = true;
                }
            }
            else if (requisition.ApplicableWorkflowType != WorkflowTypeEnum.NotApplicable)
            {
                // Get req total
                var requisitionTotal = requisition.GetRequisitionTotal();
                var hierarchy = _coidService.GetHierarchyForCoid(requisition.COID);
                var returnDTO = Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true ?
                    _approverWorkflowService.ValidateAndSaveUserWorkflowSteps(hierarchy, userName, requisition.ApplicableWorkflowType, requisition.COID, requisitionTotal) :
                    userService.ValidateUserWorkflow(userName, requisition.ApplicableWorkflowType, requisition.COID, requisitionTotal);

                if (returnDTO != null && !returnDTO.IsValid)
                {
                    requisition.IsSubmitBlocked = true;
                    requisition.Warnings.Add("Workflow adjustments required, please contact an Administrator.");
                    if (returnDTO.WarningMessages != null && returnDTO.WarningMessages.Any())
                    {
                        requisition.Warnings.AddRange(returnDTO.WarningMessages);
                    }
                }
            }

            //Check Bill-Only and Bill and Replace
            if (requisition.RequisitionType == RequisitionType.BillAndReplace || requisition.RequisitionType == RequisitionType.BillOnly)
            {
                if (requisition.RequisitionItems == null || !requisition.RequisitionItems.Any())
                {
                    requisition.IsSubmitBlocked = true;
                }
            }

            //Check capitated - bill and replace / bill only has at least one main item
            if (this.IsCapitatedReqHaveAnyMainitems(requisition))
            {
                requisition.Warnings.Add("Please add quantity to at least one main-item.");
                requisition.IsSubmitBlocked = true;
            }

            //Check capitated - bill and replace / bill only has sub-item for each main item
            if (this.IsCapitatedReqHaveSubitemsForEachMainItems(requisition))
            {
                requisition.Warnings.Add("Please add quantity to at least one sub-item for each main-item.");
                requisition.IsSubmitBlocked = true;
            }

            if (requisition.RequisitionItems != null)
            {
                IEnumerable<int> vendorAffiliateIds = getVendorAffiliateIds(userName);

                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    var isVboRequisitionInFlight = requisition.IsVendor && (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold);
                    this.ValidateRequisitionItem(requisitionItem, userName, requisition.RequisitionType, requisition.COID, requisition.DepartmentId, requisition.IsVendor, requisition.RequisitionId, requisition.IsMobile, vendorAffiliateIds, isVboRequisitionInFlight);
                    bool ItemCanceledFlag = false;
                    // For Canceled Item 
                    if (requisitionItem.IsInvalid && Convert.ToBoolean(!requisitionItem.IsActive) && requisitionItem.IsActive != null)
                    {
                        requisition.IsSubmitBlocked = true;
                        var warningMessage = string.Format("​​Item '{0}' has been canceled. Please remove this item from the requisition.", requisitionItem.Item.Id);
                        requisition.Warnings.Add(warningMessage);
                        ItemCanceledFlag = true;
                    }
                    if (requisitionItem.Item?.Vendor != null && requisitionItem.SPRDetail == null)
                    {
                        if (requisitionItem.IsInvalid && requisitionItem.Item.Vendor.InvalidVendorFlag && !ItemCanceledFlag)
                        {
                            requisition.IsSubmitBlocked = true;
                            var warningMessage = $"​​Vendor is invalid for Item '{requisitionItem.Item.Id}'. Please remove this item from the requisition.";
                            requisition.Warnings.Add(warningMessage);
                        }
                    }
                    if (requisitionItem.SPRDetail?.Vendor != null)
                    {
                        if (requisitionItem.IsInvalid && requisitionItem.SPRDetail.Vendor.InvalidVendorFlag && !ItemCanceledFlag)
                        {
                            requisition.IsSubmitBlocked = true;
                            var warningMessage = $"​​Vendor is invalid for Item '{requisitionItem.Item.Id}'. Please remove this item from the requisition.";
                            requisition.Warnings.Add(warningMessage);
                        }
                    }
                    if (requisitionItem.IsInvalid && !String.IsNullOrEmpty(requisitionItem.Warning) && !ItemCanceledFlag)
                    {
                        requisition.IsSubmitBlocked = true;
                        var warningMsg = string.Format("Item '{0}' exceeds maximum quantity because {1}. Please reduce the quantity or wait for the item to be received before placing another requisition for this item.", requisitionItem.Item.Id, requisitionItem.Warning);
                        requisition.Warnings.Add(warningMsg);
                    }

                    if (!requisitionItem.IsInvalid && requisitionItem.IsSPR && requisitionItem.SPRDetail != null && !ItemCanceledFlag)
                    {
                        //Validate SPR details
                        if (String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.ItemDescription))
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("Description is missing from an approval request item.");
                        }

                        //UOM
                        if (requisitionItem.SPRDetail.UOM == null || String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.UOM.Code))
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("UOM is missing from an approval request item.");
                        }

                        //Vendor
                        if (requisitionItem.SPRDetail.Vendor == null || requisitionItem.SPRDetail.Vendor.Id == 0)
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("Vendor is missing from an approval request item.");
                        }

                        //Part #
                        if (String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.PartNumber))
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("Vendor Part Number is missing from an approval request item.");
                        }

                        //Shipping ??
                        if (requisitionItem.SPRDetail.ShipToAddress == null)
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("Shipping Address is missing from an approval request item.");
                        }

                        //GL Acct
                        if (!isVendorUser && !(requisition.RequisitionSubmissionTypeId==1) && String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.GeneralLedgerCode))
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("General Ledger Account is missing from an approval request item.");
                        }

                        //Additional Information
                        if (isVendorUser && String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.AdditionalInformation))
                        {
                            requisition.IsSubmitBlocked = true;
                            requisition.Warnings.Add("Additional Information is missing from an approval request item.");
                        }
                    }
                }
            }

            return requisition;
        }

        public RequisitionItem ValidateRequisitionItem(RequisitionItem requisitionItem, string userName, RequisitionType requisitionType, string coid, string dept, bool IsVboRequisition, int requisitionId, bool isMobile = false, IEnumerable<int> vendorAffiliateIds = null, bool isVboRequisitionInFlight = false)
        {
            //Reset flag, try again
            requisitionItem.Warning = null;
            requisitionItem.IsInvalid = false;
            if (requisitionItem.RequisitionItemStatusType != RequisitionItemStatusType.ItemNotOrdered)
            {
                requisitionItem.RequisitionItemStatusType = RequisitionItemStatusType.Valid;
            }

            //If user is a vendor, check if item's vendor is an affiliate
            if (vendorAffiliateIds != null)
            {
                if (!requisitionItem.IsSPR && requisitionItem?.Item?.Vendor != null && !vendorAffiliateIds.Any(x => x == requisitionItem.Item.Vendor.Id))
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.InvalidVendor);
                }
                if (requisitionItem.IsSPR && requisitionItem?.SPRDetail?.Vendor != null && !vendorAffiliateIds.Any(x => x == requisitionItem.SPRDetail.Vendor.Id))
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.InvalidVendor);
                }
            }

            if (requisitionItem?.ParItem != null || requisitionItem?.Item?.Id != null)
            {
                string itemId = requisitionItem?.ParItem != null ? requisitionItem?.ParItem?.ItemId.ToString() : requisitionItem?.Item?.Id;
                if (((requisitionItem.SPRDetail == null && !requisitionItem.IsWastePar && requisitionItem.Discount == null) || (requisitionItem.SPRDetail != null && !requisitionItem.SPRDetail.EstimatedPriceHasChanged &&
                    !requisitionItem.SPRDetail.PartNumberHasChanged && !requisitionItem.SPRDetail.UOMHasChanged && !requisitionItem.SPRDetail.VendorHasChanged && !requisitionItem.SPRDetail.DescriptionHasChanged)) &&
                    !isMobile && (requisitionType == RequisitionType.Standard || requisitionType == RequisitionType.BillOnly || requisitionType == RequisitionType.BillAndReplace || requisitionType == RequisitionType.CapitatedBillOnly || requisitionType == RequisitionType.CapitatedBillAndReplace || requisitionType == RequisitionType.Rush))
                {
                    int itemNum;
                    if (int.TryParse(itemId, out itemNum))
                    {
                        requisitionItem.IsActive = this.GetItemStatusByItemNum(userName, coid, itemId, requisitionId);
                    }
                    else
                    {
                        requisitionItem.IsActive = this.GetItemStatusByReorderNum(userName, coid, itemId);
                    }
                }

                if ((!requisitionItem.IsInvalid) && !Convert.ToBoolean(requisitionItem.IsActive) && requisitionItem.IsActive != null)
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.CanceledItem);
                }
                if ((!requisitionItem.IsInvalid) && requisitionItem.Item.Vendor != null && requisitionItem.Item.Vendor.InvalidVendorFlag)
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.InvalidVendor);
                }
                if (requisitionItem.SPRDetail?.Vendor != null)
                {
                    if ((!requisitionItem.IsInvalid) && requisitionItem.Item.Vendor != null && requisitionItem.SPRDetail.Vendor.InvalidVendorFlag)
                    {
                        requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.InvalidVendor);
                    }
                }
            }

            //Check item
            if (!requisitionItem.IsInvalid && !requisitionItem.IsSPR && requisitionType != RequisitionType.Capital)
            {
                int intItemId;
                if (requisitionItem.Item == null || String.IsNullOrWhiteSpace(requisitionItem.Item.Id) || !int.TryParse(requisitionItem.Item.Id, out intItemId) || String.IsNullOrWhiteSpace(requisitionItem.Item.Description))
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.ItemNotFound);
                }
            }

            //Check for Hold items
            if (isVboRequisitionInFlight)
            {
                if (requisitionItem.VboHoldItemConversion == null && requisitionItem.ParItem == null)
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.Hold);
                }
            }

            //Check for PAR items
            if (!requisitionItem.IsInvalid && !requisitionItem.IsSPR && requisitionType != RequisitionType.Capital && !(IsVboRequisition && string.Equals(requisitionItem.Item?.ComplianceCode, "C", StringComparison.InvariantCultureIgnoreCase)))
            {
                if (requisitionItem.AvailableParItems == null || !requisitionItem.AvailableParItems.Any())
                {
                    requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.NoParsAvailable);
                }
            }

            //Check quantity
            if (!requisitionItem.IsInvalid)
            {
                int maxQty = 0;

                //Get QOH & Max for specified PAR
                if (requisitionItem.ParItem != null && requisitionItem.SPRDetail == null && requisitionItem.Discount == null && !requisitionItem.IsWastePar)
                {
                    maxQty = (int)requisitionItem.ParItem.MaxQuantityToOrder;
                    var inFlightQty = this.GetInFlightQuantity(userName, coid, dept, requisitionItem.ParItem.ParId, requisitionItem.ParItem.ItemId.ToString());
                    //If trying to order more than Max when it's not BillOnly, BillandReplace, CapitatedBillOnly, CapitatedBillandReplace
                    if (!isMobile && !this.IsOfBillOnlyRequisitionTypes(requisitionType))
                    {
                        if (requisitionItem.QuantityToOrder > maxQty)
                        {
                            requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.MaxQtyExceeded);
                        }
                        else if (requisitionItem.QuantityToOrder > (maxQty - inFlightQty.InFlightQuantity))
                        {
                            requisitionItem.Warning = string.Format("QTY of {0} is already On-Order for this PAR", inFlightQty.InFlightQuantity.ToString());
                            requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.MaxQtyExceeded);
                        }
                    }

                }
            }

            if (!requisitionItem.IsInvalid && requisitionItem.IsWastePar && (requisitionItem.Discount == null || requisitionItem.Discount < 0 || requisitionItem.Discount > 100))
            {
                requisitionItem = SetRequisitionItemInvalid(requisitionItem, RequisitionItemStatusType.NoDiscount);
            }

            //Validate Procedure date
            if (requisitionItem.ProcedureDate != null)
            {
                //Procedure date cannot be in the future
                if (requisitionItem.ProcedureDate > DateTime.Now)
                {
                    requisitionItem.ProcedureDate = null;
                }
            }

            return requisitionItem;
        }

        private RequisitionDTO SaveRequsitionToService(Requisition requisition, string userName)
        {
            //Transform to DTO
            var reqDTO = ConvertProcedureDateToLocalTime(new RequisitionDTO(requisition));

            log.Debug(string.Format("Method: SaveRequsitionToService"));

            //Send to requisition service
            var returnedReqDTO = ApiUtility.ExecuteApiPostWithContentTo<RequisitionDTO>(reqAPIEndpoint, saveRequisitionMethod, new Dictionary<string, string>() { { "userName", userName } }, reqDTO);

            return returnedReqDTO;
        }

        private RequisitionDTO SaveRequsitionToServiceAsApprover(Requisition requisition, string userName)
        {

            //Transform to DTO
            var reqDTO = ConvertProcedureDateToLocalTime(new RequisitionDTO(requisition));

            //Send to requisition service
            var returnedReqDTO = ApiUtility.ExecuteApiPostWithContentTo<RequisitionDTO>(reqAPIEndpoint, saveRequisitionAsApproverMethod, new Dictionary<string, string>() { { "userName", userName } }, reqDTO);

            return returnedReqDTO;
        }

        public Requisition SubmitRequisition(Requisition requisition, string userName, long? cartId = null)
        {
            if (Convert.ToBoolean(featureflagsection["FFVPROBadgeInBE"]) && requisition.RequisitionItems[0].ProcedureDateHasChanged && requisition.VProBadgeLog != null)
            {
                requisition = this.GetVProBadgeInStatus(requisition);
            }
            if (Convert.ToBoolean(featureflagsection["FFVPROBadgeInBE"]) && requisition.IsVendor && requisition.VProBadgeLog == null)
            {

               requisition = this.GetVProBadgeInStatus(requisition);
            }

            var sprItemsForVira = new List<RequisitionItem>();
            ICacheManager<object> cacheManager = new RedisCacheManager<object>();
            ICacheProvider cacheProvider = new CacheProvider(cacheManager);
            var isRequisitionerAVendorUser = userService.UserHasVendorPart(requisition.CreatedBy);
            var viraFeatureFlagOn = featureflagsection["ViraAutomationOn"];
            var isVendor = false;

            isVendor = requisition.IsVendor;

            requisition = SetRequisitionAsVendorType(requisition, isRequisitionerAVendorUser);
            if (requisition.RequisitionItems != null)
            {
                //Combine duplicates (merging quantities)
                if (requisition.RequisitionType != RequisitionType.CapitatedBillAndReplace && requisition.RequisitionType != RequisitionType.CapitatedBillOnly)
                {
                    requisition.RequisitionItems = requisition.RequisitionItems.Where(x => x.SPRDetail == null && x.Item != null && x.ParItem != null && x.Discount == null).GroupBy(o => new { o.Item.Id, o.ParItem.ParId })
                                                        .Select(g => g.Skip(1).Aggregate(g.First(), (a, o) => { a.QuantityToOrder += o.QuantityToOrder; a.IsRushOrder = o.IsRushOrder ? o.IsRushOrder : a.IsRushOrder; return a; }))
                                                        .Union(requisition.RequisitionItems.Where(x => x.Discount != null))
                                                        .Union(requisition.RequisitionItems.Where(x => x.SPRDetail != null))
                                                        .Union(requisition.RequisitionItems.Where(x => string.Equals(x.Item?.ComplianceCode, "C") && requisition.IsVendor)).ToList();
                }

                var itemList = requisition.RequisitionItems.ToList();

                IEnumerable<int> vendorAffiliateIds = getVendorAffiliateIds(requisition.CreatedBy);

                for (int i = 0; i < itemList.Count(); i++)
                {
                    var isVboRequisitionInFlight = requisition.IsVendor && (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold);
                    itemList[i] = this.ValidateRequisitionItem(itemList[i], userName, requisition.RequisitionType, requisition.COID, requisition.DepartmentId, requisition.IsVendor, requisition.RequisitionId, requisition.IsMobile, vendorAffiliateIds, isVboRequisitionInFlight);
                }
                requisition.RequisitionItems = itemList;
            }

            if (this.IsRushReqConvertToStandardReq(requisition) && requisition.RequisitionType == RequisitionType.Rush)
            {
                requisition.RequisitionType = RequisitionType.Standard;
            }
            //Save Req
            var returnedReqDTO = this.SaveRequsitionToService(requisition, userName);

            var requesterParts = userService.GetUserParts(userName);
            RequisitionDTO returnedSubmittedReqDTO;
            if (requesterParts.Where(x => x.AppPartType == AppPartType.MyApprovals).Any())
            {
                returnedSubmittedReqDTO = ApiUtility.ExecuteApiPostWithContentTo<RequisitionDTO>(reqAPIEndpoint, submitApproversRequisitionMethod, new Dictionary<string, string>() { { "userName", userName }, { "cartId", cartId.ToString() }, }, returnedReqDTO);
            }
            else
            {
                log.Debug("Just before Method: submitRequisitionMethod");
                //Call Req service to submit
                returnedSubmittedReqDTO = ApiUtility.ExecuteApiPostWithContentTo<RequisitionDTO>(reqAPIEndpoint, submitRequisitionMethod, new Dictionary<string, string>() { { "userName", userName }, { "cartId", cartId.ToString() }, }, returnedReqDTO);
            }

            //Transform back to UI object
            string fullName = GetUserFullName(returnedSubmittedReqDTO.CreatedBy);

            var returnRequisition = new Requisition(returnedSubmittedReqDTO, fullName);
            if (returnRequisition != null && returnRequisition.RequisitionItems != null && requisition.RequisitionItems != null)
            {
                foreach (var reqItem in returnRequisition.RequisitionItems.Where(x => x.Item != null))
                {
                    reqItem.Item = requisition.RequisitionItems.Where(x => x.Item != null && x.Item.Id == reqItem.Item.Id).Select(y => y.Item).FirstOrDefault();
                    if (reqItem.ParItem != null)
                    {
                        reqItem.AvailableParItems = requisition.RequisitionItems.Where(x => x.ParItem != null
                                                                                && x.ParItem.ParId == reqItem.ParItem.ParId
                                                                                && x.ParItem.ItemId == reqItem.ParItem.ItemId
                                                                                && (x.ParItem.MainItemId == null || x.ParItem.MainItemId == reqItem.ParItem.MainItemId))
                                                                            .Select(y => y.AvailableParItems).FirstOrDefault();

                        reqItem.ParItem = requisition.RequisitionItems.Where(x => x.ParItem != null
                                                                                && x.ParItem.ParId == reqItem.ParItem.ParId
                                                                                && x.ParItem.ItemId == reqItem.ParItem.ItemId
                                                                                && (x.ParItem.MainItemId == null || x.ParItem.MainItemId == reqItem.ParItem.MainItemId))
                                                                            .Select(y => y.ParItem).FirstOrDefault();
                    }
                }
                //TO DO: check to see if all non par items are SPR items or have SPR Detail object
                sprItemsForVira = returnRequisition.RequisitionItems.Where(x => x.SPRDetail != null).ToList();
            }
            foreach (var item in requisition.RequisitionItems)
            {
                var newCacheKey = $"{itemCachePrefix}_{item.Item.Id}_{requisition.COID}_{requisition.RequisitionId}";
                cacheProvider.RemoveCache(newCacheKey);
            }
            if (Convert.ToBoolean(viraFeatureFlagOn) && isVendor && sprItemsForVira != null)
            {
                var maxAttempts = 3;
                var attempt = 0;
                var cancellationTokenSource = new CancellationTokenSource();
                var token = cancellationTokenSource.Token;

                Task.Run(async () =>
                {
                    while (attempt < maxAttempts && !token.IsCancellationRequested)
                    {
                        var result = RunViraAutomation(sprItemsForVira, returnRequisition.RequisitionId, returnRequisition.COID);
                        if (result)
                        {
                            cancellationTokenSource.Cancel();
                            break;
                        }
                        else
                        {
                            attempt++;
                            await Task.Delay(viraRetryInterval, token);
                        }
                    }
                }, token);

            }
            return returnRequisition;
        }

        public Requisition CreateRequisitionFromTemplate(Requisition template, string userName)
        {
            //Save
            this.SaveRequisition(template, userName);

            //Copy to req
            var requisition = template;
            requisition.RequisitionId = 0;
            requisition.Comments = null;
            requisition.RequisitionDate = DateTime.Now;
            requisition.RequisitionStatusType = RequisitionStatusType.Draft;

            if (requisition.RequisitionItems != null)
            {
                foreach (var item in requisition.RequisitionItems)
                {
                    item.RequisitionItemId = 0;
                    item.LotSerialPairs = new List<LotSerialPair>() { new LotSerialPair() { ClinicalUseDetailsId = 0 } };

                    item.QuantityOnHand = null;
                    item.QuantityInFlight = 0;
                }
            }

            return requisition;
        }

        public Requisition ConvertRequisitionToTemplate(Requisition requisition, string userName)
        {
            //Save unfinished work for drafts
            if (requisition.RequisitionStatusType == RequisitionStatusType.Draft)
            {
                this.SaveRequisition(requisition, userName);
            }

            var isNotEditable = requisition.RequisitionStatusType != RequisitionStatusType.Draft && requisition.RequisitionStatusType != RequisitionStatusType.Recalled && requisition.RequisitionStatusType != RequisitionStatusType.Denied && requisition.RequisitionStatusType != RequisitionStatusType.SubmissionError;

            //Copy req
            var newTemplate = requisition;

            //Clear out unused data and reset statuses
            newTemplate.RequisitionId = 0;
            newTemplate.ApprovalStep = null;
            newTemplate.RequisitionStatusType = RequisitionStatusType.Template;
            newTemplate.RequisitionDate = DateTime.Now;
            newTemplate.Comments = null;
            newTemplate.IsRequisitionEditable = true;
            newTemplate.IsMobile = false;
            if (newTemplate.RequisitionItems != null)
            {
                //Remove SPR Items
                newTemplate.RequisitionItems = newTemplate.RequisitionItems.Where(x => x.ParItem != null && x.ParItem.ParId != null && (x.ParentRequisitionItemId == null || x.ParentRequisitionItemId == 0)).ToList();


                var itemDetails = new List<ItemDetailsDTO>();

                if (isNotEditable)
                {
                    var distinctItemParDTOList = newTemplate.RequisitionItems.GroupBy(item => item.ParItem.ItemId).Select(grp => grp.First()).Select(x => new ItemParDTO() { ItemId = x.ParItem.ItemId.ToString(), ParId = x.ParItem.ParId });

                    itemDetails = ApiUtility.ExecuteApiPostWithContentTo<List<ItemDetailsDTO>>(reqAPIEndpoint, getMultipleItemsWithDetailsMethod, new Dictionary<string, string>()
                                                                                                                        {
                                                                                                                            { "userName", userName },
                                                                                                                            { "COID", newTemplate.COID },
                                                                                                                            { "departmentId", newTemplate.DepartmentId.ToString() }
                                                                                                                        }, distinctItemParDTOList);
                }


                foreach (var item in newTemplate.RequisitionItems)
                {
                    //reset QuantityOnHand and Quantity in Flight in case req was Mobile
                    item.QuantityOnHand = null;
                    item.QuantityInFlight = 0;

                    if (isNotEditable)
                    {
                        var details = itemDetails.Where(x => x.Item.Id == item.ParItem.ItemId.ToString()).FirstOrDefault();
                        if (details != null)
                        {
                            var availableParItems = details.AvailableParItems.ToList();
                            item.Item = details.Item;
                            if (availableParItems != null && availableParItems.Any())
                            {
                                var parId = item.ParItem.ParId;
                                var mainItemId = item.ParItem.MainItemId;
                                item.AvailableParItems = availableParItems;
                                item.ParItem = item.AvailableParItems.FirstOrDefault(x => x.ParId == parId).CloneMe();
                                item.ParItem.MainItemId = mainItemId;

                            }
                        }
                    }


                    item.RequisitionItemId = 0;
                    item.RequisitionItemStatusType = item.IsInvalid ? item.RequisitionItemStatusType : RequisitionItemStatusType.Unknown;
                    item.RequisitionScheduledDate = null;
                    item.IsRushOrder = false;
                    item.ClinicalUseId = null;
                    item.PatientId = null;
                    item.PatientName = null;
                    item.Provider = null;
                    item.ProcedureDate = null;
                    item.UpchargeCost = null;
                    item.CreateDate = DateTime.Now;
                    item.PONumber = null;
                    item.ParentSystemId = null;
                    item.QuantityFulfilled = null;
                    item.IsPurged = false;

                    if (requisition.IsBillOnlyInfoAvailable)
                    {
                        item.LotSerialPairs = new List<LotSerialPair>() { new LotSerialPair() { ClinicalUseDetailsId = 0, LotNumber = "", SerialNumber = "" } };
                    }
                }
            }

            return newTemplate;
        }

        public Requisition SaveRequisition(Requisition requisition, string userName)
        {
            var iClassParItems = requisition.RequisitionItems?.Where(x => x.ParItem?.ParId.Contains("I") == true).ToList();
            log.Debug("Method: SaveRequisition");
            if (!this.SmartLegacySubmissionAvailabilityCheck(userName, requisition.COID))
            {
                log.Error(String.Format("SmartLegacySubmissionAvailabilityCheck failed for COID: {0} -- requisition is NOT saved", requisition.COID));
                requisition.IsSaveBlocked = true;
                return requisition;
            }
            if (requisition.RequisitionStatusType == RequisitionStatusType.Draft && iClassParItems.Any())
            {
                requisition.Warnings = new List<string>();
                requisition.IsSubmitBlocked = true;
                requisition.Warnings.Add(iClassParNotificationMessage);
                return requisition;
            }

            requisition = DeduplicatingItemsBeforeSaving(requisition, userName, false);

            var isRequisitionerAVendorUser = (requisition.CreatedBy == null) ? userService.UserHasVendorPart(userName) : userService.UserHasVendorPart(requisition.CreatedBy);
            requisition = SetRequisitionAsVendorType(requisition, isRequisitionerAVendorUser);

            var returnedReqDTO = this.SaveRequsitionToService(requisition, userName);

            return RemappingRequisitionItemsForRequisitionDto(returnedReqDTO, requisition, userName, isRequisitionerAVendorUser);
        }

        public Requisition SaveRequisitionAsApprover(Requisition requisition, string userName)
        {
            requisition = DeduplicatingItemsBeforeSaving(requisition, userName, true);

            var isRequisitionerAVendorUser = userService.UserHasVendorPart(requisition.CreatedBy);
            requisition = SetRequisitionAsVendorType(requisition, isRequisitionerAVendorUser);

            var returnedReqDTO = this.SaveRequsitionToServiceAsApprover(requisition, userName);

            return RemappingRequisitionItemsForRequisitionDto(returnedReqDTO, requisition, userName);

        }

        public StatusUpdateDTO UpdateRequisitionStatus(int requisitionId, string userName, RequisitionStatusType requisitionStatusType, string comments)
        {
            string viraFeatureFlag = featureflagsection["ViraAutomation"];
            bool isVendor = false;            
            var statusUpdateDTO = new StatusUpdateDTO() { IsStatusUpdated = false, WarningMessages = new List<string>() };

            //TODO: Once ViraSPRItemRequest is mapped and is ready to be sent to Vira automation in below SubscribeViraMessageQue, delete temporaryRequisitionIdForVira and the
            //assignment of requisition.RequisitionId to it
            int temporaryRequisitionIdForVira = 0;

            int? currentStep = null;
            if (requisitionStatusType == RequisitionStatusType.Approved || requisitionStatusType == RequisitionStatusType.Denied || requisitionStatusType == RequisitionStatusType.Recalled)
            {
                var requisitionDTO = ApiUtility.ExecuteApiGetTo<RequisitionDTO>(reqAPIEndpoint, getRequisitionForUpdateMethod, new Dictionary<string, string>()
                                                                                                                            {
                                                                                                                                { "requisitionId", requisitionId.ToString() },
                                                                                                                                { "userName", userName }
                                                                                                                            });
                var requisition = new Requisition(requisitionDTO, null);
                isVendor = requisition.IsVendor;

                if (isVendor)
                {
                    temporaryRequisitionIdForVira = requisition.RequisitionId;
                    //TODO: Create logic mapping out viraSPRItemRequest (ex: viraSPRItemRequest = requisition.item.info)
                    IEnumerable<FacilityWorkflowStep> facilityWorkflowSteps = facilityWorkflowService.Get(requisition.COID, WorkflowTypeEnum.Vendor).Steps;
                    requisition = this.DetermineApproveDenyEligibility(requisition, facilityWorkflowSteps, userName);
                    requisition = this.DetermineSmartAvailability(requisition, facilityWorkflowSteps, userName, requisitionStatusType == RequisitionStatusType.Approved, requisition.COID);
                }
                else
                {
                    IEnumerable<UserWorkflowStep> userWorkflowSteps = userService.GetUserWorkflowSteps(requisition.CreatedBy, requisition.COID, (int)requisition.ApplicableWorkflowType);
                    requisition = this.DetermineApproveDenyEligibility(requisition, userWorkflowSteps, userName);
                    requisition = this.DetermineSmartAvailability(requisition, userWorkflowSteps, userName, requisitionStatusType == RequisitionStatusType.Approved, requisition.COID);
                }


                if (requisition.RequisitionStatusType == RequisitionStatusType.Submitted && (int)requisition.ApplicableWorkflowType != (int)WorkflowTypeEnum.NotApplicable)
                {
                    requisition = DetermineReviewAvailable(requisition, userName);
                }

                if (requisition == null)
                {
                    throw new InvalidOperationException("NO REQUISITION FOUND. CANNOT UPDATE STATUS.");
                }

                //Warnings from an Approver standpoint
                if (requisition.IsSubmitToSmartLegacyBlocked)
                {
                    statusUpdateDTO.WarningMessages.Add("NOT UPDATED: SMART cannot establish a connection with external systems; they may be undergoing maintenance. The requisition has been saved but not advanced. Please contact an administrator and/or try again later.");
                }
                else
                {
                    var isAvailable = ((int)requisitionStatusType == (int)RequisitionStatusType.Recalled || ((int)requisitionStatusType == (int)RequisitionStatusType.Denied && requisition.IsDenyAvailable) || ((int)requisitionStatusType == (int)RequisitionStatusType.Approved && requisition.IsApproveAvailable));
                    if (!isAvailable || (isAvailable && !(requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold)))
                    {
                        statusUpdateDTO.WarningMessages.Add(UpdateStatusWarnings.GetUpdateStatusWarningMessage(requisitionStatusType, requisition.RequisitionStatusType));
                    }
                }

                if (statusUpdateDTO.WarningMessages.Any())
                {
                    statusUpdateDTO.CurrentRequisitionStatusType = (int)requisition.RequisitionStatusType;
                    statusUpdateDTO.CurrentRequisitionStatus = requisition.RequisitionStatus;
                    return statusUpdateDTO;
                }

                currentStep = requisition.ApprovalStep;
            }

            var reqHistory = new
            {
                Comments = comments,
                RequisitionStatusTypeId = (int)requisitionStatusType,
                CreateDate = DateTime.Now,
                CreatedBy = userName,
                RequisitionId = requisitionId,
                CurrentStep = currentStep
            };

            statusUpdateDTO = ApiUtility.ExecuteApiPostWithContentTo<StatusUpdateDTO>(reqAPIEndpoint, updateRequisitionStatusMethod, null, reqHistory);         
            return statusUpdateDTO;
        }

        private Requisition CreateNewTemplate(string userName)
        {
            var template = this.CreateNewRequisition(userName);
            template.RequisitionStatusType = RequisitionStatusType.Template;

            return template;
        }

        private Requisition CreateNewPunchOutRequisition(string userName)
        {
            var requisition = this.CreateNewRequisition(userName);
            requisition.RequisitionType = RequisitionType.PunchOut;

            return requisition;
        }

        private Requisition CreateNewCapitalRequisition(string userName)
        {
            var requisition = this.CreateNewRequisition(userName);
            requisition.RequisitionType = RequisitionType.Capital;

            return requisition;
        }

        private Requisition CreateNewRequisition(string username)
        {
            var defaultFacility = GetDefaultFacility(username);
            return new Requisition()
            {
                RequisitionDate = DateTime.Now,
                RequisitionStatusType = RequisitionStatusType.Draft,
                RequisitionType = Enums.RequisitionType.Standard,
                COID = defaultFacility != null ? defaultFacility.COID : "",
                Facility = defaultFacility,
                DepartmentId = "",
                Department = null,
                RequisitionItems = new List<RequisitionItem>(),
                IsRequisitionEditable = true,
                IsVendor = userService.UserHasVendorPart(username)
            };
        }

        private Requisition CreateNewRushRequisition(string userName)
        {
            var defaultFacility = GetDefaultFacility(userName);
            return new Requisition()
            {
                RequisitionDate = DateTime.Now,
                RequisitionStatusType = RequisitionStatusType.Draft,
                RequisitionType = Enums.RequisitionType.Rush,
                COID = defaultFacility != null ? defaultFacility.COID : "",
                Facility = defaultFacility,
                DepartmentId = "",
                Department = null,
                RequisitionItems = new List<RequisitionItem>(),
                IsRequisitionEditable = true
            };
        }

        private Facility GetDefaultFacility(string username)
        {
            Facility defaultFacility = null;
            var facilities = profileService.GetFacilities(username);
            if (facilities != null)
            {
                defaultFacility = facilities.FirstOrDefault(x => x.IsDefault) ?? facilities.FirstOrDefault();
            }

            return defaultFacility;
        }

        public IEnumerable<Requisition> GetRequisitionsWithItemStatuses(string userName, string COID, DateTime startDate, DateTime endDate, int departmentId)
        {
            var requisitionDTOs = ApiUtility.ExecuteApiGetTo<List<RequisitionDTO>>(reqAPIEndpoint, getRequisitionsWithItemStatusesByCOIDDatesMethod, new Dictionary<string, string>() {
                                                                                                            { "userName", userName },
                                                                                                            { "COID", COID },
                                                                                                            { "startDate", startDate.ToString() },
                                                                                                            { "endDate", endDate.ToString() },
                                                                                                            { "departmentId", departmentId.ToString() }
                                                                                                        });

            return this.HydrateRequisitions(userName, requisitionDTOs);
        }

        public RequisitionListResultsDTO GetRequisitionsByVendor(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionListResultsDTO>(reqAPIEndpoint, getRequisitionsByVendorMethod, null, request.ToJsonContent());

            reqReportResultsDto.Requisitions = reqReportResultsDto.Requisitions.Select(x => this.HydrateReqFacilityAndDepartment(x, facilityNames, departmentNames)).ToList();

            return reqReportResultsDto;
        }

        public RequisitionReportExportResultsDTO GetRequisitionsByVendorReportExport(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionReportExportResultsDTO>(reqAPIEndpoint, getRequisitionsByVendorReportExportMethod, null, request.ToJsonContent());

            return reqReportResultsDto;
        }

        public RequisitionListResultsDTO GetRequisitionsForReport(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionListResultsDTO>(reqAPIEndpoint, getRequisitionsForReportMethod, null, request.ToJsonContent());

            reqReportResultsDto.Requisitions = reqReportResultsDto.Requisitions.Select(x => this.HydrateReqFacilityAndDepartment(x, facilityNames, departmentNames)).ToList();

            return reqReportResultsDto;
        }


        public RequisitionListResultsDTO GetVBORequisitionsForReport(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionListResultsDTO>(reqAPIEndpoint, getVBORequisitionsForReportMethod, null, request.ToJsonContent());

            reqReportResultsDto.Requisitions = reqReportResultsDto.Requisitions.Select(x => this.HydrateReqFacilityAndDepartment(x, facilityNames, departmentNames)).ToList();

            return reqReportResultsDto;
        }

        public RequisitionReportExportResultsDTO GetRequisitionsForReportExport(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionReportExportResultsDTO>(reqAPIEndpoint, getRequisitionsForReportExportMethod, null, request.ToJsonContent());

            return reqReportResultsDto;
        }

        public RequisitionReportExportResultsDTO GetVBORequisitionsForReportExport(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionReportExportResultsDTO>(reqAPIEndpoint, getVBORequisitionsForReportExportMethod, null, request.ToJsonContent());

            return reqReportResultsDto;
        }

        public RequisitionListResultsDTO GetRequisitionsForReportByItemNumber(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            // Storing facility/department names in a dictionary to reduce memory footprint while Req API call is active
            IDictionary<string, string> facilityNames = DictionaryOfFacilityNames(usersSocFacilities);
            IDictionary<string, string> departmentNames = DictionaryOfDepartmentNames(usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionListResultsDTO>(reqAPIEndpoint, getRequisitionsForReportByItemNumberMethod, null, request.ToJsonContent());

            reqReportResultsDto.Requisitions = reqReportResultsDto.Requisitions.Select(x => this.HydrateReqFacilityAndDepartment(x, facilityNames, departmentNames)).ToList();

            return reqReportResultsDto;
        }

        public RequisitionReportExportResultsDTO GetRequisitionsForReportByItemNumberExport(RequisitionReportRequestDto request)
        {
            request.VendorAffiliations = request.IsVendorUser ? getVendorAffiliateIds(request.Username)?.ToList() : null;

            // Getting all facility/department names from the user's Span of Control
            var usersSocFacilities = profileService.GetFacilities(request.Username).ToList();
            var usersSocDepartments = usersSocFacilities.SelectMany(x => profileService.GetAllDepartmentsFromCache(request.Username, x.COID).ToList()).ToList();

            // Testing the text filter against facility/department names here rather than in sproc because we don't store those in the DB
            // Matching facilities (COID only) and departments (full LocationIdentifer) are passed to sproc to be used in filtering check
            request.FacilitiesMatchedOnFilter = FacilityNameFilterMatching(request.FilterText, usersSocFacilities);
            request.DepartmentsMatchedOnFilter = DepartmentNameFilterMatching(request.FilterText, usersSocDepartments);

            var reqReportResultsDto = ApiUtility.ExecuteApiPostTo<RequisitionReportExportResultsDTO>(reqAPIEndpoint, getRequisitionsForReportByItemNumberExportMethod, null, request.ToJsonContent());

            return reqReportResultsDto;
        }

        private IEnumerable<Requisition> HydrateRequisitions(string userName, List<RequisitionDTO> requisitionDTOs)
        {
            var requisitions = new List<Requisition>();
            if (requisitionDTOs != null)
            {
                //Show reqs where not deleted
                var userList = new List<Profile>();
                var facilityList = new List<Facility>();
                var deptList = new List<Department>();
                foreach (var requisitionDTO in requisitionDTOs)
                {
                    //Get full name
                    string fullName = GetUserFullName(requisitionDTO.CreatedBy);

                    //Map to main object
                    var requisition = new Requisition(requisitionDTO, fullName);

                    //Add in facility details
                    requisition.Facility = profileService.GetFacility(requisition.COID, userName);
                    if (requisition.Facility != null)
                    {
                        facilityList.Add(requisition.Facility);
                    }

                    //Add in dept details
                    requisition.Department = profileService.GetDepartment(userName, requisition.COID, requisition.DepartmentId);
                    if (requisition.Department != null)
                    {
                        deptList.Add(requisition.Department);
                    }

                    requisitions.Add(requisition);
                }
            }

            return requisitions;
        }

        private IEnumerable<TDto> HydrateDtos<TDto>(string userName, IEnumerable<TDto> dtos)
            where TDto : new()
        {
            if (dtos != null)
            {
                foreach (var dto in dtos)
                {
                    string fullName = "";
                    PropertyInfo createdByProperty = typeof(TDto).GetProperty("CreatedBy");
                    PropertyInfo locationIdentifierProperty = typeof(TDto).GetProperty("LocationIdentifier");
                    if (createdByProperty != null)
                    {
                        string createdBy = (string)createdByProperty.GetValue(dto);
                        fullName = GetUserFullName(createdBy);
                    }

                    if (locationIdentifierProperty != null)
                    {
                        string locationIdentifier = (string)locationIdentifierProperty.GetValue(dto);
                        string[] parts = locationIdentifier.Split('_');

                        if (parts.Length == 2)
                        {
                            string coid = parts[0];
                            string departmentId = parts[1];

                            PropertyInfo facilityProperty = typeof(TDto).GetProperty("Facility");
                            if (facilityProperty != null)
                            {
                                facilityProperty.SetValue(dto, profileService.GetFacility(coid, userName));
                            }

                            PropertyInfo departmentProperty = typeof(TDto).GetProperty("Department");
                            if (departmentProperty != null)
                            {
                                departmentProperty.SetValue(dto, profileService.GetDepartment(userName, coid, departmentId));
                            }
                        }
                        else
                        {
                            throw new ArgumentException("Invalid LocationIdentifier format. It should be in the format 'COID_DepartmentId'.");
                        }
                    }
                    else
                    {
                        throw new ArgumentException("The 'LocationIdentifier' property was not found on the DTO.");
                    }
                }
            }
            return dtos;
        }

        public List<RequisitionDTO> GetRequisitions(string userName, string COID, DateTime startDate, DateTime endDate, int departmentId)
        {
            return ApiUtility.ExecuteApiGetTo<List<RequisitionDTO>>(reqAPIEndpoint, getRequisitionsByCOIDDatesMethod, new Dictionary<string, string>() {
                                                                                                                { "COID", COID },
                                                                                                                { "startDate", startDate.ToString() },
                                                                                                                { "endDate", endDate.ToString() },
                                                                                                                { "userName", userName},
                                                                                                                { "departmentId", departmentId.ToString() }
                                                                                                            });
        }


        public PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport(PurchasingRequisitionReportParameters reportParameters)
        {
            var reqPurchasingReportResults = ApiUtility.ExecuteApiPostTo<PurchasingRequisitionReportResultsDTO>(reqAPIEndpoint, getRequisitionsForPurchasingReportMethod, null, reportParameters.ToJsonContent());

            if(reqPurchasingReportResults.TotalCount != 0 && !reqPurchasingReportResults.Requisitions.IsNullOrEmpty())
            {
                reqPurchasingReportResults.Requisitions = HydrateDtos(reportParameters.UserName, reqPurchasingReportResults.Requisitions);

                foreach (PurchasingRequisitionDTO requisition in reqPurchasingReportResults.Requisitions)
                {
                    GetFileAttachmentURLGeneric(requisition);
                }
            }
            return reqPurchasingReportResults;
        }

        /// <summary>
        /// Gets lists of vendors, req types, and buyers for the advanced filters on the Purchasing based on a list of COIDs
        /// </summary>
        /// <param name="filterList"></param>
        /// <returns></returns>
        public RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList)
        {
            var reqPurchasingAdvancedFiltersResults = ApiUtility.ExecuteApiPostTo<RequisitionPurchasingAdvancedFiltersDto>(reqAPIEndpoint, getAdvancedFiltersForPurchasingMethod, null, filterList.ToJsonContent());
            return reqPurchasingAdvancedFiltersResults;
        }

        public List<Requisition> GetRequisitionAndItemsByPONumber(int poNumber, string coid, string userName)
        {
            var reqPurchasingReportResults = ApiUtility.ExecuteApiGetTo<List<RequisitionDTO>>(reqAPIEndpoint, _getRequisitionsByPONumber, new Dictionary<string, string>()
            {
                {"poNumber", poNumber.ToString() },
                {"coid", coid }
            });

            List<Requisition> hydratedRequisitions = HydrateRequisitions(userName, reqPurchasingReportResults).ToList();

            foreach (var requisition in hydratedRequisitions)
            {
                requisition.RequisitionItems.RemoveAll(ri => ri.PONumber != poNumber);
                GetFileAttachmentURL(requisition);
            }

            return hydratedRequisitions;
        }

        private string GetUserFullName(string userAccountName)
        {
            string fullName = null;
            var user = userService.GetUser(userAccountName);
            if (user != null)
            {
                fullName = user.FirstName + " " + user.LastName;
            }
            else
            {
                var profile = userService.GetProfile(userAccountName);
                if (profile != null)
                {
                    fullName = GetUserFullName(profile);
                }
                else
                {
                    fullName = userAccountName;
                }
            }


            return fullName;
        }

        private string GetUserFullNameWithoutDomain(string userAccountName)
        {
            string fullName = userAccountName;
            //PLEASE KEEP IN MERGE: This null check is needed -- userAccountName is sometimes null in UK
            if (!string.IsNullOrEmpty(userAccountName) && userAccountName.ToLower() != gpoUserId.ToLower())
            {
                var user = userService.GetUserWithoutDomain(userAccountName);
                if (user != null)
                {
                    fullName = user.FirstName + " " + user.LastName;
                }
            }
            return fullName;
        }

        private string GetUserFullName(Profile profile)
        {
            return profile.FirstName + " " + profile.LastName;
        }

        private string GetUserFullName(UserProfile userProfile)
        {
            return userProfile.FirstName + " " + userProfile.LastName;
        }

        public RequisitionWorkflowDTO GetRequisitionWorkflow(int requisitionId, string userName)
        {
            var requisition = this.GetRequisition(requisitionId, userName);
            if (requisition == null)
            {
                throw new ArgumentException(String.Format("No requisition found with requisitionId = {0}", requisitionId));
            }
            var requisitionWorkflowDTO = new RequisitionWorkflowDTO()
            {
                ApplicableWorkflowType = requisition.ApplicableWorkflowType,
                RequisitionTotal = requisition.GetRequisitionTotal(),
                RequisitionWorkflowSteps = new List<RequisitionWorkflowStepDTO>()
            };

            if (requisition.ApplicableWorkflowType != WorkflowTypeEnum.NotApplicable)
            {
                //Get requisition history
                var reqHistory = new List<RequisitionStatusHistory>();

                reqHistory = this.GetRequisitionHistory(requisitionId).ToList();
                if (reqHistory != null && reqHistory.Count() != 0)
                {
                    if (requisition.RequisitionStatusType != RequisitionStatusType.Denied && requisition.RequisitionStatusType != RequisitionStatusType.SubmissionError && requisition.RequisitionStatusType != RequisitionStatusType.Recalled)
                    {
                        if (reqHistory.Any(x => x.RequisitionStatusType == RequisitionStatusType.Denied || x.RequisitionStatusType == RequisitionStatusType.SubmissionError || x.RequisitionStatusType == RequisitionStatusType.Recalled))
                        {
                            var lastDenialOrSubmissionErrorOrCancelId = reqHistory.Where(x => x.RequisitionStatusType == RequisitionStatusType.Denied || x.RequisitionStatusType == RequisitionStatusType.SubmissionError || x.RequisitionStatusType == RequisitionStatusType.Recalled).Max(y => y.Id);
                            reqHistory = reqHistory.Where(x => x.Id > lastDenialOrSubmissionErrorOrCancelId
                                                            && (x.RequisitionStatusType == RequisitionStatusType.Submitted
                                                            || x.RequisitionStatusType == RequisitionStatusType.Approved
                                                            || x.RequisitionStatusType == RequisitionStatusType.PendingApproval
                                                            || x.RequisitionStatusType == RequisitionStatusType.OnHold
                                                            )).ToList();
                        }
                    }
                    else if ((requisition.RequisitionStatusType == RequisitionStatusType.Denied || requisition.RequisitionStatusType == RequisitionStatusType.SubmissionError || requisition.RequisitionStatusType == RequisitionStatusType.Recalled) && (reqHistory.Where(x => x.RequisitionStatusType == RequisitionStatusType.Denied || x.RequisitionStatusType == RequisitionStatusType.SubmissionError || x.RequisitionStatusType == RequisitionStatusType.Recalled).Count() > 1))
                    {
                        var lastDenialOrSubmissionErrorId = reqHistory.Where(x => x.RequisitionStatusType == RequisitionStatusType.Denied || x.RequisitionStatusType == RequisitionStatusType.SubmissionError || x.RequisitionStatusType == RequisitionStatusType.Recalled).Max(y => y.Id);
                        var secondToLastDenialId = reqHistory.Where(x => (x.RequisitionStatusType == RequisitionStatusType.Denied || x.RequisitionStatusType == RequisitionStatusType.SubmissionError || x.RequisitionStatusType == RequisitionStatusType.Recalled) && x.Id < lastDenialOrSubmissionErrorId).Max(y => y.Id);
                        reqHistory = reqHistory.Where(x => x.Id > secondToLastDenialId
                                                            && (x.RequisitionStatusType == RequisitionStatusType.Denied
                                                            || x.RequisitionStatusType == RequisitionStatusType.Approved
                                                            || x.RequisitionStatusType == RequisitionStatusType.PendingApproval
                                                            || x.RequisitionStatusType == RequisitionStatusType.OnHold
                                                            || x.RequisitionStatusType == RequisitionStatusType.Recalled
                                                            )).ToList();
                    }

                }

                //************************************************************************************************************
                int stepToCheck = 0;
                foreach (var reqHistoryRecord in reqHistory.OrderBy(x => x.Id))
                {
                    if (reqHistoryRecord.ApprovalStep != null)
                    {
                        stepToCheck = (int)reqHistoryRecord.ApprovalStep;
                        string stepText = "Step " + stepToCheck.ToString();

                        if (requisition.IsVendor && reqHistoryRecord.ApprovalStep == 0)
                        {
                            stepText = sssApproverStepLabel;
                            reqHistoryRecord.ApprovedAmount = null;
                            reqHistoryRecord.IsCERReviewer = false;
                        }

                        switch (reqHistoryRecord.RequisitionStatusType)
                        {
                            case RequisitionStatusType.Approved:
                                requisitionWorkflowDTO.RequisitionWorkflowSteps.Add(new RequisitionWorkflowStepDTO()
                                {
                                    ApprovedAmount = reqHistoryRecord.ApprovedAmount,
                                    IsCERReviewer = reqHistoryRecord.IsCERReviewer,
                                    RequisitionWorkflowStepType = RequisitionWorkflowStepType.Approved,
                                    Step = stepText,
                                    UpdatedDate = reqHistoryRecord.CreateDate,
                                    UserName = reqHistoryRecord.CreatedByFullName
                                });
                                break;
                            case RequisitionStatusType.Denied:
                                requisitionWorkflowDTO.RequisitionWorkflowSteps.Add(new RequisitionWorkflowStepDTO()
                                {
                                    RequisitionWorkflowStepType = RequisitionWorkflowStepType.Denied,
                                    Step = stepText,
                                    UpdatedDate = reqHistoryRecord.CreateDate,
                                    UserName = reqHistoryRecord.CreatedByFullName
                                });
                                break;
                            case RequisitionStatusType.Recalled:
                                requisitionWorkflowDTO.RequisitionWorkflowSteps.Add(new RequisitionWorkflowStepDTO()
                                {
                                    RequisitionWorkflowStepType = RequisitionWorkflowStepType.Recalled,
                                    Step = stepText,
                                    UpdatedDate = reqHistoryRecord.CreateDate,
                                    UserName = reqHistoryRecord.CreatedByFullName
                                });
                                break;
                            case RequisitionStatusType.PendingApproval:
                                //Determine if auto-advanced, if auto-advanced, add in as auto-advance step
                                if (reqHistoryRecord.Comments != null && reqHistoryRecord.Comments.Contains("Requisition auto-advanced"))
                                {
                                    requisitionWorkflowDTO.RequisitionWorkflowSteps.Add(new RequisitionWorkflowStepDTO()
                                    {
                                        RequisitionWorkflowStepType = RequisitionWorkflowStepType.Bypassed,
                                        Step = "Step " + ((int)reqHistoryRecord.ApprovalStep - 1).ToString(),
                                        UpdatedDate = reqHistoryRecord.CreateDate,
                                        UserName = reqHistoryRecord.CreatedByFullName,
                                        ApprovedAmount = reqHistoryRecord.ApprovedAmount,
                                        IsCERReviewer = reqHistoryRecord.IsCERReviewer
                                    });
                                }
                                break;
                        }
                    }
                }

                if (requisition.IsVendor)
                {
                    var facilityWorkflowSteps = new List<FacilityWorkflowStep>();

                    if (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold || requisition.RequisitionStatusType == RequisitionStatusType.Draft)
                    {
                        facilityWorkflowSteps = facilityWorkflowService.Get(requisition.COID, WorkflowTypeEnum.Vendor)?.Steps.ToList();
                        var saveFacilityWorkflowRequest = new SaveFacilityWorkflowDTO()
                        {
                            Workflow = new FacilityWorkflowDTO()
                            {
                                Steps = facilityWorkflowSteps,
                                WorkflowTypeId = (int)WorkflowTypeEnum.Vendor,
                                Coid = requisition.COID
                            }
                        };
                        if (Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true) 
                        {
                            facilityWorkflowService.ValidateAndSaveFacilityWorkflow(saveFacilityWorkflowRequest);
                        }
                    }

                    if (facilityWorkflowSteps != null)
                    {
                        foreach (FacilityWorkflowStep facilityWorkflowStep in facilityWorkflowSteps.Where(x => x.Approver?.User != null))
                        {
                            var stepType = RequisitionWorkflowStepType.NA;
                            if (requisition.ApprovalStep != null && requisition.RequisitionStatusType != RequisitionStatusType.Draft)
                            {
                                stepType = RequisitionWorkflowStepType.Current;
                            }

                            string approverName = facilityWorkflowStep.Approver.User.UserProfile != null ? facilityWorkflowStep.Approver.User.UserProfile.FirstName + " " + facilityWorkflowStep.Approver.User.UserProfile.LastName : facilityWorkflowStep.Approver.User.FirstName + " " + facilityWorkflowStep.Approver.User.LastName;
                            requisitionWorkflowDTO.RequisitionWorkflowSteps.Add(new RequisitionWorkflowStepDTO()
                            {
                                RequisitionWorkflowStepType = stepType,
                                Step = sssApproverStepLabel,
                                UserName = approverName
                            });
                        }
                    }
                }
                else if (!requisition.IsVendor)
                {
                    //Get future and current workflow steps
                    var userWorkflowSteps = new List<UserWorkflowStep>();
                    if (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval)
                    {
                        userWorkflowSteps = userService.GetUserWorkflowSteps(requisition.CreatedBy, requisition.COID, (int)requisition.ApplicableWorkflowType)
                                                            .Where(x => x.Step >= (requisition.ApprovalStep ?? 0)).ToList();
                    }
                    else if (requisition.RequisitionStatusType == RequisitionStatusType.Draft)
                    {
                        userWorkflowSteps = userService.GetUserWorkflowSteps(requisition.CreatedBy, requisition.COID, (int)requisition.ApplicableWorkflowType).ToList();
                    }

                    //Map remaining future steps (if any exist)
                    if (userWorkflowSteps != null)
                    {
                        foreach (var userWorkflowStep in userWorkflowSteps.Where(x => x.Approver != null && x.Approver.User != null).OrderBy(y => y.Step).ThenBy(s => requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? s.Approver.CapitalMaxApprovalAmount : s.Approver.MaxApprovalAmount))
                        {
                            var stepType = RequisitionWorkflowStepType.NA;
                            if (requisition.ApprovalStep != null && userWorkflowStep.Step == (int)requisition.ApprovalStep && requisition.RequisitionStatusType != RequisitionStatusType.Draft)
                            {
                                stepType = RequisitionWorkflowStepType.Current;
                            }

                            var stepText = "Step " + userWorkflowStep.Step.ToString();

                            if (userWorkflowStep.DelegatedByUserId == null)
                            {
                                if (userWorkflowStep.IsFinalStep)
                                {
                                    stepText = "Final";
                                }
                                if (userWorkflowStep.IsFinalRushStep)
                                {
                                    stepText = "Final Rush";
                                }
                            }
                            else
                            {
                                //If this approver is a delegate, check if regular approver(s) at this step are final or final rush
                                if (userWorkflowSteps.Where(x => x.Step == userWorkflowStep.Step && x.DelegatedByUserId == null && x.Approver != null && x.IsFinalStep).Any())
                                {
                                    stepText = "Final";
                                }
                                if (userWorkflowSteps.Where(x => x.Step == userWorkflowStep.Step && x.DelegatedByUserId == null && x.Approver != null && x.IsFinalRushStep).Any())
                                {
                                    stepText = "Final Rush";
                                }
                            }

                            var userWorkflowStepMaxApprovalAmountForWorkflowType = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? userWorkflowStep.Approver.CapitalMaxApprovalAmount : userWorkflowStep.Approver.MaxApprovalAmount;
                            string approverName = userWorkflowStep.Approver.User.UserProfile != null ? userWorkflowStep.Approver.User.UserProfile.FirstName + " " + userWorkflowStep.Approver.User.UserProfile.LastName : userWorkflowStep.Approver.User.FirstName + " " + userWorkflowStep.Approver.User.LastName;
                            requisitionWorkflowDTO.RequisitionWorkflowSteps.Add(new RequisitionWorkflowStepDTO()
                            {
                                RequisitionWorkflowStepType = stepType,
                                Step = stepText,
                                IsCERReviewer = userWorkflowStep.Approver.IsCERReviewer,
                                ApprovedAmount = userWorkflowStep.IsFinalRushStep ? null : (decimal?)userWorkflowStepMaxApprovalAmountForWorkflowType,
                                UserName = approverName
                            });
                        }
                    }
                }
            }

            return requisitionWorkflowDTO;
        }

        public void DeleteAttachment(FileNamesDTO fileNames)
        {
            ApiUtility.ExecuteApiPostWithContentTo<bool>(reqAPIEndpoint, deleteAttachment, null, fileNames);
        }

        protected Requisition DeduplicatingItemsBeforeSaving(Requisition requisition, string userName, bool saveAsApprover)
        {
            //Validate when saving 
            var dedupedItems = new List<RequisitionItem>();

            //De-duplicating to ensure accuracy of qty limits
            if (requisition.RequisitionType != RequisitionType.CapitatedBillAndReplace && requisition.RequisitionType != RequisitionType.CapitatedBillOnly && requisition.RequisitionType != RequisitionType.Capital)
            {
                //De-dupe PAR items
                dedupedItems = requisition.RequisitionItems.Where(x => x.Item != null && x.ParItem != null && !x.IsSPR && !x.IsWastePar).GroupBy(o => new { o.Item.Id, o.ParItem.ParId })
                                                            .Select(g => g.Skip(1).Aggregate(g.First(), (a, o) => { a.QuantityToOrder += o.QuantityToOrder; a.IsRushOrder = o.IsRushOrder ? o.IsRushOrder : a.IsRushOrder; return a; })).ToList();
                //Ensure Waste PAR items stay in
                dedupedItems.AddRange(requisition.RequisitionItems.Where(x => x.IsWastePar && x.ParItem != null));

                //Ensure SPR items stay in
                dedupedItems.AddRange(requisition.RequisitionItems.Where(x => x.ParItem == null));
            }
            if (dedupedItems.Count > 0 && requisition.RequisitionType != RequisitionType.Capital)
            {
                for (int i = 0; i < dedupedItems.Count(); i++)
                {
                    var isVboRequisitionInFlight = requisition.IsVendor && (requisition.RequisitionStatusType == RequisitionStatusType.PendingApproval || requisition.RequisitionStatusType == RequisitionStatusType.OnHold);
                    dedupedItems[i] = this.ValidateRequisitionItem(dedupedItems[i], userName, requisition.RequisitionType, requisition.COID, requisition.DepartmentId, requisition.IsVendor, requisition.RequisitionId, requisition.IsMobile, null, isVboRequisitionInFlight);
                }

                if (dedupedItems != null)
                {
                    foreach (var item in dedupedItems)
                    {
                        foreach (var reqItem in requisition.RequisitionItems.Where(x => x.Item != null && x.ParItem != null && item.ParItem != null && item.Item != null && x.Item.Id == item.Item.Id && x.ParItem.ParId == item.ParItem.ParId))
                        {
                            reqItem.RequisitionItemStatusType = item.RequisitionItemStatusType;
                            reqItem.IsInvalid = item.IsInvalid;
                        }
                        if (item.LotSerialPairs != null)
                        {
                            if (saveAsApprover)
                            {
                                item.LotSerialPairs = item.LotSerialPairs.Where(x => !(string.IsNullOrEmpty(x.LotNumber) && string.IsNullOrEmpty(x.SerialNumber) && x.ClinicalUseDetailsId == 0)).ToList();
                            }
                            else
                            {
                                item.LotSerialPairs = item.LotSerialPairs.Where(x => !(string.IsNullOrEmpty(x.LotNumber) && string.IsNullOrEmpty(x.SerialNumber))).ToList();
                            }
                        }
                    }

                    requisition.RequisitionItems = dedupedItems;
                }
            }
            if (requisition.RequisitionType == RequisitionType.CapitatedBillAndReplace || requisition.RequisitionType == RequisitionType.CapitatedBillOnly)
            {
                if (requisition.RequisitionItems != null)
                {
                    foreach (var item in requisition.RequisitionItems)
                    {
                        if (item.LotSerialPairs != null)
                        {
                            if (saveAsApprover)
                            {
                                item.LotSerialPairs = item.LotSerialPairs.Where(x => !(string.IsNullOrEmpty(x.LotNumber) && string.IsNullOrEmpty(x.SerialNumber) && x.ClinicalUseDetailsId == 0)).ToList();
                            }
                            else
                            {
                                item.LotSerialPairs = item.LotSerialPairs.Where(x => !(string.IsNullOrEmpty(x.LotNumber) && string.IsNullOrEmpty(x.SerialNumber))).ToList();
                            }
                        }
                    }
                }
            }

            return requisition;
        }

        protected Requisition RemappingRequisitionItemsForRequisitionDto(RequisitionDTO requisitionDto, Requisition requisition, string userName, bool isVendorUser = false)
        {
            //Transform back to UI object
            var fullName = GetUserFullName(requisitionDto.CreatedBy);

            var returnRequisition = new Requisition(requisitionDto, fullName)
            {
                Department = requisition.Department,
                Warnings = requisition.Warnings
            };

            //Need to put item/par details back in
            if (requisition.RequisitionItems != null && returnRequisition.RequisitionItems != null)
            {
                foreach (var returnReqItem in returnRequisition.RequisitionItems)
                {
                    var matchingReqItem = requisition.RequisitionItems.FirstOrDefault(x => x.TrackerIndex == returnReqItem.TrackerIndex);
                    returnReqItem.AvailableParItems = matchingReqItem?.AvailableParItems;
                    returnReqItem.ParItem = matchingReqItem?.ParItem;
                    returnReqItem.Item = matchingReqItem?.Item;
                }
            }

            returnRequisition = this.ValidateRequisition(returnRequisition, userName, isVendorUser);

            return returnRequisition;
        }

        public InFlightQty GetInFlightQuantity(string userName, string coid, string dept, string parClass, string itemId)
        {
            var test = ApiUtility.ExecuteApiGetTo<InFlightQty>(reqAPIEndpoint, getInFlightQuantity, new Dictionary<string, string>()
            {
                { "userName", userName },
                { "coid", coid },
                { "dept", dept },
                { "parClass", parClass },
                { "itemId", itemId }
            });
            return test;
        }

        private RequisitionDTO ConvertProcedureDateToLocalTime(RequisitionDTO req)
        {
            foreach (var reqItem in req.RequisitionItems)
            {
                //Note: Workaround since DB is logging as LocalTime.
                if (reqItem.ProcedureDate != null)
                {
                    var localTime = reqItem.ProcedureDate.Value.ToLocalTime();
                    while (localTime.Day != reqItem.ProcedureDate.Value.Day)
                    {
                        if (localTime < reqItem.ProcedureDate)
                        {
                            localTime = localTime.AddHours(1);
                        }
                        else
                        {
                            localTime = localTime.AddHours(-1);
                        }
                    }
                    reqItem.ProcedureDate = localTime;
                }
            }

            return req;
        }

        private bool IsOfBillOnlyRequisitionTypes(RequisitionType requisitionType)
        {
            return requisitionType == RequisitionType.BillAndReplace
                   || requisitionType == RequisitionType.BillOnly
                   || requisitionType == RequisitionType.CapitatedBillAndReplace
                   || requisitionType == RequisitionType.CapitatedBillOnly;
        }

        private bool? GetItemStatusByItemNum(string userName, string coid, string itemId, int requisitionId)
        {
            ICacheManager<object> cacheManager = new RedisCacheManager<object>();
            ICacheProvider cacheProvider = new CacheProvider(cacheManager);

            return cacheProvider.ProvideItemStatusCache(itemId, userName, coid, requisitionId);
        }
        private bool? GetItemStatusByReorderNum(string userName, string coid, string reorderNum)
        {
            return ApiUtility.ExecuteApiGetTo<List<ItemInfoModel>>(reqAPIEndpoint, getItemInfoByReorderNbr, new Dictionary<string, string>()
            {
                { "userName", userName },
                { "coid", coid },
                { "reorderNumber", reorderNum }
            })?.FirstOrDefault()?.IsActive;
        }


        private Requisition getRequisitionForVendorUser(int requisitionId, string username, string coid, List<int> vendorAffiliations)
        {
            var request = new RequisitionForVendorUserRequestDto
            {
                RequisitionId = requisitionId,
                Username = username,
                Coid = coid,
                VendorAffiliations = vendorAffiliations
            };

            var requisitionDto = ApiUtility.ExecuteApiPostTo<RequisitionWithDetailsDTO>(reqAPIEndpoint, getRequisitionForVendorUserMethod, null, request.ToJsonContent());

            return requisitionDto != null
                ? ToRequisition(requisitionDto, username, false)
                : null;
        }

        private Requisition GetFileAttachmentURL(Requisition requisition)
        {
            for (int i = 0; i < requisition.RequisitionItems.Count(); i++)
            {

                if (requisition.RequisitionItems[i].SPRDetail != null)
                {
                    if (requisition.RequisitionItems[i].SPRDetail.FileAttachments.Any())
                    {
                        var factory = GetFileUtility.CreateandReturnObj(requisition.Facility.SmartCountryCode[0]);
                        foreach (var file in requisition.RequisitionItems[i].SPRDetail.FileAttachments)
                        {
                            if (!String.IsNullOrWhiteSpace(file.FileName))
                            {
                                file.FilePath = factory.GetFileLink(file.FileName);
                                var fileUploader = userService.GetUser(file.CreatedBy);
                                if (fileUploader != null)
                                {
                                    file.CreatedBy = fileUploader.FirstName + " " + fileUploader.LastName;
                                }
                            }
                        }
                    }
                }
            }
            return requisition;
        }

        private bool RunViraAutomation(IEnumerable<RequisitionItem> sprItemForVira, int requisitionId, string coid)
        {
            try
            {
                var statusUpdateDTO = new StatusUpdateDTO() { IsStatusUpdated = false, WarningMessages = new List<string>() };
                ViraSPRItemRequest viraSPRItemRequest = null;
                var requisitionItemId = 0;
                var successfulConnectionToVira = false;

                foreach (var item in sprItemForVira)
                {
                    viraSPRItemRequest = new ViraSPRItemRequest(item, coid);
                    requisitionItemId = item.RequisitionItemId;
                    successfulConnectionToVira = _viraService.SubscribeViraMessageQue(viraSPRItemRequest, requisitionId);
                    var message = $"{MethodBase.GetCurrentMethod().GetMethodName()} : Vira Automation failed to process the request. Please contact an administrator.";
                    var getViraItemStatusRecordForCreateUpdate = GetViraItemStatusRecord(requisitionId, requisitionItemId);
                    if (!successfulConnectionToVira)
                    {
                        if (getViraItemStatusRecordForCreateUpdate == null)
                        {
                            CreateViraItemStatusRecord(requisitionItemId, requisitionId, successfulConnectionToVira);
                        }
                        else if (getViraItemStatusRecordForCreateUpdate != null && getViraItemStatusRecordForCreateUpdate.RetryCount <= 2)
                        {
                            UpdateViraItemsStatusRecord(getViraItemStatusRecordForCreateUpdate, successfulConnectionToVira);
                            if (getViraItemStatusRecordForCreateUpdate.RetryCount == 2)
                            {
                                statusUpdateDTO.WarningMessages.Add("Vira Automation failed to process the request. Please contact an administrator.");
                                log.Error(message);
                            }
                        }
                        else
                        {
                            log.Error(message);
                        }
                    }
                //TODO: Evaluate if needs to be a return object with this boolean and a status to update based on Vira response
                }                              
                return successfulConnectionToVira;
            }
            catch (Exception ex)
            {
                log.Error("An error occurred in RunViraAutomation method.", ex);
                throw;
            }
        }

        private void UpdateViraItemsStatusRecord(ViraItemStatus getViraItemStatusRecordForCreateUpdate, bool successfulConnectionToVira)
        {
            try
            {
                getViraItemStatusRecordForCreateUpdate.RetryCount++;
                getViraItemStatusRecordForCreateUpdate.PublishStatus = successfulConnectionToVira? true : false;
                getViraItemStatusRecordForCreateUpdate.LastRetry = DateTime.Now;
                getViraItemStatusRecordForCreateUpdate.ViraApprovalStatus = getViraItemStatusRecordForCreateUpdate.RetryCount <= 2 ? "Pending Vira" : "Failed";
                ApiUtility.ExecuteApiPostTo<string>(reqAPIEndpoint, updateViraItemStatusRecord, null, getViraItemStatusRecordForCreateUpdate.ToJsonContent());
            }
            catch (Exception ex)
            {
                log.Error("Error updating Vira item status record", ex);
                throw;
            }
        }

        private void CreateViraItemStatusRecord(int requisitionItemId, int requisitionId, bool successfulConnectionToVira)
        {
            try
            {
                var viraItemStatusRecord = new ViraItemStatus()
                {
                    RequisitionItemId = requisitionItemId,
                    RequisitionId = requisitionId,
                    PublishStatus = successfulConnectionToVira ? true : false,
                    RetryCount= 1,
                    LastRetry = DateTime.Now,
                    ViraApprovalStatus = "Pending Vira",
                };
                var createItemStatusRecord = ApiUtility.ExecuteApiPostTo<ViraItemStatus>(reqAPIEndpoint, createViraItemStatusRecord, null, viraItemStatusRecord.ToJsonContent());
            }
            catch (Exception ex)
            {
                log.Error("Error creating Vira item status record", ex);
                throw;
            }
        }

        private ViraItemStatus GetViraItemStatusRecord(int requisitionId, int requisitionItemId)
        {
            try
            {
                return ApiUtility.ExecuteApiGetTo<ViraItemStatus>(reqAPIEndpoint, getViraItemStatusRecord, new Dictionary<string, string>()
                {
                    { "requisitionItemId", requisitionItemId.ToString() },
                    { "requisitionId", requisitionId.ToString() }
                });
            }
            catch (Exception ex)
            {
                log.Error("Error getting Vira item status record", ex);
                throw;
            }
        }

        private T GetFileAttachmentURLGeneric<T>(T entity) where T : class
        {
            // Get the RequisitionItems property
            PropertyInfo requisitionItemsProperty = typeof(T).GetProperty("RequisitionItems");
            if (requisitionItemsProperty != null)
            {
                // Get the value of the RequisitionItems property (assuming it's a list)
                var requisitionItems = (IEnumerable)requisitionItemsProperty.GetValue(entity);
                if (requisitionItems != null)
                {
                    foreach (var item in requisitionItems)
                    {
                        // Get the SPRDetail property
                        PropertyInfo sprDetailProperty = item.GetType().GetProperty("SPRDetail");
                        if (sprDetailProperty != null)
                        {
                            var sprDetail = sprDetailProperty.GetValue(item);

                            // Get the FileAttachments property
                            PropertyInfo fileAttachmentsProperty = sprDetail?.GetType().GetProperty("FileAttachments");
                            if (fileAttachmentsProperty != null)
                            {
                                var fileAttachments = (IEnumerable)fileAttachmentsProperty.GetValue(sprDetail);

                                if (fileAttachments != null)
                                {
                                    // Get the Facility property (from the original entity)
                                    PropertyInfo facilityProperty = typeof(T).GetProperty("Facility");
                                    var facility = facilityProperty?.GetValue(entity);

                                    // Get the SmartCountryCode property (from the Facility)
                                    PropertyInfo smartCountryCodeProperty = facility?.GetType().GetProperty("SmartCountryCode");
                                    var smartCountryCode = (string)smartCountryCodeProperty?.GetValue(facility);

                                    var factory = GetFileUtility.CreateandReturnObj(smartCountryCode[0]);

                                    foreach (var file in fileAttachments)
                                    {
                                        // Get the FileName property
                                        PropertyInfo fileNameProperty = file.GetType().GetProperty("FileName");
                                        var fileName = (string)fileNameProperty?.GetValue(file);

                                        if (!string.IsNullOrWhiteSpace(fileName))
                                        {
                                            // Get the FilePath property
                                            PropertyInfo filePathProperty = file.GetType().GetProperty("FilePath");
                                            filePathProperty?.SetValue(file, factory.GetFileLink(fileName));

                                            // Get the CreatedBy property
                                            PropertyInfo createdByProperty = file.GetType().GetProperty("CreatedBy");
                                            var createdBy = (string)createdByProperty?.GetValue(file);

                                            var fileUploader = userService.GetUser(createdBy);
                                            if (fileUploader != null)
                                            {
                                                createdByProperty?.SetValue(file, $"{fileUploader.FirstName} {fileUploader.LastName}");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return entity;
        }
        
        private Requisition GetVProBadgeInStatus(Requisition requisition)
        {
            try
            {
                log.Info($"Logging VPRO BadgeIn on Req# {requisition.RequisitionId} submitted at {DateTime.Now}. VPro Details : {requisition.VProBadgeLog}"); 
                var vproId = (requisition.VProBadgeLog != null && requisition.VProBadgeLog.Id > 0) ? requisition.VProBadgeLog.Id : 0;
                var parsedUserName = requisition.CreatedBy.Split('/')[1];
                var badgeInRequest = new VPROBadgeInRequest
                {
                    id = vproId,
                    coid = requisition.COID,
                    userName = parsedUserName,
                    procedureDateTime = requisition.RequisitionItems[0].ProcedureDate.ToString(),
                    requisitionId = requisition.RequisitionId
                };

                var vproDetails = userService.GetAndStoreVProBadgeInDetails(badgeInRequest);
                requisition.BadgeLogId = vproDetails.Id;
                requisition.VProBadgeLog = new RequisitionVProBadgeLog
                {
                    Id = vproDetails.Id,
                    RequisitionId = requisition.RequisitionId,
                    BadgeInStatusId = vproDetails.VproBadgeVerified
                };

                return requisition;
            }
            catch (Exception ex)
            {
                log.Error($"Error while trying to get the VPro badge status", ex);
                throw new InvalidOperationException("Failed to get VPro badge status.", ex);
            }
        }
    }

    public class AcquisitionType
    {
        public string Sys_id { get; set; }
        public string Label { get; set; }
        public string Value { get; set; }
    }

    public class EquipmentType
    {
        public string Sys_id { get; set; }
        public string Dependency { get; set; }
        public string Name { get; set; }
        public string Active { get; set; }
        public string Type { get; set; }
    }

    public class EntryExitTypes
    {
        public AcquisitionType[] acqTypes { get; set; }

        public EquipmentType[] equTypes { get; set; }
    }

    public class EntryExitAcquisition
    {
        public AcquisitionType[] result { get; set; }
    }

    public class EntryExitEquipment
    {
        public EquipmentType[] result { get; set; }
    }

    public class ParticipatingFacilityType
    {
        public string Ge_account_number { get; set; }
        public string Sys_id { get; set; }
        public int Unit_number { get; set; }
        public int Coid { get; set; }
        public string Participating { get; set; }
        public string Facility { get; set; }
    }

    public class EntryExitParticipatingFacilities
    {
        public ParticipatingFacilityType[] result { get; set; }
    }

}
