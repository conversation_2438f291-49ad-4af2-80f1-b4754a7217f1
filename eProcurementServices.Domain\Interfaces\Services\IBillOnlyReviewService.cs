﻿using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Model.BillOnlyReview;
using System.Threading.Tasks;

namespace eProcurementServices.Domain.Interfaces.Services
{
    /// <summary>
    /// Represents a service for managing bill only review requisitions.
    /// </summary>
    public interface IBillOnlyReviewService
    {
        /// <summary>
        /// Retrieves bill only review requisitions based on the specified request.
        /// </summary>
        /// <param name="request">The request containing the search criteria.</param>  
        /// <returns>A list of bill only review search results.</returns>  
        Task<PaginatedBORResult> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request);

        /// <summary>
        /// Generates a PDF for bill only review requisitions based on the specified request.
        /// </summary>
        /// <param name="request">The request containing the search criteria.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a byte array of the generated PDF document.
        /// </returns>
        Task<byte[]> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request);
    }
}
