﻿using System.Collections.Generic;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.DTO;

namespace eProcurementServices.Domain.Interfaces.Services
{
    public interface IUserService
    {
        void SaveUserEditInfo(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO);
        User GetUser(string accountName);
        User GetUserWithoutDomain(string accountName);
        void UpdateUsersName(string domain, string userName);
        UserEditDTO GetUserEditUsers(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID);
        IEnumerable<UserProfile> GetEProcurementUserProfiles(string coid);
        IEnumerable<AppPart> GetUserParts(string domain, string username);
        IEnumerable<AppPart> GetUserParts(string domainSlashUserName);
        UserProfile GetUserProfile(string domainSlashUserName);
        /// <summary>
        /// Gets user information by userName from
        /// smartclientauthorization/users/{userName}/applications/{appId}
        /// </summary>
        /// <param name="domain">User Domain</param>
        /// <param name="userName">User Name</param>
        /// <returns></returns>
        UserProfile GetUserProfile(string domain, string userName);
        Profile GetProfile(string domain, string userName);
        Profile GetProfile(string domainSlashUserName);
        /// <summary>
        /// Replaces GetProfile(string domain, string userName)
        /// to call the new Security API instead of Home API
        /// </summary>
        /// <param name="domain"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Profile GetSecurityProfile(string domain, string userName);
        IEnumerable<WorkflowType> GetAllWorkflowTypes();
        IEnumerable<WorkflowType> GetAllUserWorkflowTypes();
        IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, int? workflowTypeId = null);
        IEnumerable<WorkflowExportDTO> GetUserWorkflowsForExport(WorkflowExportInputDTO workflowExportInputDTO);
        IEnumerable<UserWorkflowDTO> SaveWorkflows(string saversUserName, SaveWorkflowsDTO saveWorkflowsDTO);
        IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string userName, int workflowTypeId, string workflowUser, IEnumerable<UserWorkflowStep> userWorkflowSteps);
        WorkflowValidationDTO ValidateUserWorkflow(string userName, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal = null);
        ValidationOfUserWorkflowsDTO GetValidationOfUserWorkflows(ValidateUserWorkflowsRequestDTO validateUserWorkflowsRequestDTO);
        bool UserHasVendorPart(string username);
        IEnumerable<UserReportInfoDTO> RetrieveUserReportInfo(UserReportInfoRequestDTO userReportInfoRequestDTO);
        /// <summary>
        /// Gets user information based on roleId and spanOfControl from
        /// smartclientauthorization/roles/{roleId}/objectId/{objectId}/hierarchyType/{hierarchyType}/applicationId/{applicationId}/withDetails
        /// </summary>
        /// <param name="roleId">One of values from eProcurementServices.Domain.Utility.ProcurementRoles</param>
        /// <param name="spanOfControl">eProcurementServices.Domain.Model.Profile.SpanOfControl</param>
        /// <returns>Collection of UserProfile</returns>
        IEnumerable<UserProfile> GetUsersWithRoleAndSOC(string roleId, SpanOfControl spanOfControl);
        /// <summary>
        /// Check is VPro User is Badged In
        /// </summary>
        /// <param name="request"> We will send a Vpros UserName and return if they are badged in</param>
        /// <returns></returns>
        VPROResponseFiltered GetAndStoreVProBadgeInDetails(VPROBadgeInRequest request);
        /// <summary>
        /// Updates the domain for the complete username by environment.
        /// </summary>
        /// <param name="userName"></param>
        /// <returns> a string with an environment updated domain attached to the username</returns>
        string UpdateDomainByEnvironment(string userName);
        /// <summary>
        /// Gets a list of user profiles based on the workflow steps
        /// </summary>
        /// <param name="steps"> Workflow steps</param>
        /// <returns>A list of user profiles</returns>
        List<UserProfile> GetApproverProfileListForWorkflow(IEnumerable<UserWorkflowStep> steps);

        /// <summary>
        /// Processes a list of facility approver user profiles for workflow approvers.
        /// Ensures that each user profile in the list is properly initialized for use in workflow approval scenarios,
        /// such as setting up SpanOfControl and VendorAffils collections if they are null, and adding any test SpanOfControl values as needed.
        /// </summary>
        /// <param name="facilityApproverProfileList">A list of user profiles representing facility approvers.</param>
        /// <returns>A processed list of user profiles ready for workflow approval logic.</returns>
        List<UserProfile> ProcessFacilityUserProfilesForWorkflowApprovers(List<UserProfile> facilityApproverProfileList);

        /// <summary>
        /// Generates and returns a security token for authenticating requests to external security APIs.
        /// The token is typically used as a Bearer token in the Authorization header for subsequent API calls.
        /// </summary>
        /// <returns>A string containing the Bearer token to be used for API authentication.</returns>
        string AddSecurityToken();
    }
}
