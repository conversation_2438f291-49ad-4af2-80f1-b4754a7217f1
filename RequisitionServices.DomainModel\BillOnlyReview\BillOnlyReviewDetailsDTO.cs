﻿using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.VPro;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.Repositories
{
    /// <summary>
    /// Data transfer object representing the details of a Bill Only Review requisition.
    /// </summary>
    public class BillOnlyReviewDetailsDTO
    {
        /// <summary>
        /// Gets or sets the requisition identifier.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status type identifier.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status type description.
        /// </summary>
        public string RequisitionStatusTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the requisition type identifier.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition type description.
        /// </summary>
        public string RequisitionTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the patient identifier.
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// Gets or sets the patient name.
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// Gets or sets the provider name.
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// Gets or sets the location identifier.
        /// </summary>
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the date the requisition was created.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created the requisition.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is for a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type identifier.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the first name of the requisitioner.
        /// </summary>
        public string RequisitionerFirstName { get; set; }

        /// <summary>
        /// Gets or sets the last name of the requisitioner.
        /// </summary>
        public string RequisitionerLastName { get; set; }

        /// <summary>
        /// Gets or sets the country code.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets the procedure date, if available.
        /// </summary>
        public DateTime? ProcedureDate { get; set; }

        /// <summary>
        /// Gets or sets the collection of Bill Only Review item details for this requisition.
        /// </summary>
        public IEnumerable<BillOnlyReviewItemDetailsDTO> RequisitionItems { get; set; }

        /// <summary>
        /// Gets or sets the VPro badge log associated with the requisition.
        /// </summary>
        public RequisitionVProBadgeLog VProBadgeLog { get; set; }

        /// <summary>
        /// Gets or sets the digital sign-off information for the requisition.
        /// </summary>
        public RequisitionDigitalSignOff DigitalSignOff { get; set; }

        /// <summary>
        /// Gets or sets the digital sign-off user information.
        /// </summary>
        public DigitalSignOffUser DigitalSignOffUser { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewDetailsDTO"/> class
        /// using the specified <see cref="BillOnlyReviewDAO"/> object.
        /// </summary>
        /// <param name="billOnlyReviewDAO">The data access object to map from.</param>
        public BillOnlyReviewDetailsDTO(BillOnlyReviewDAO billOnlyReviewDAO)
        {
            RequisitionId = billOnlyReviewDAO.RequisitionId;
            RequisitionStatusTypeId = billOnlyReviewDAO.RequisitionStatusTypeId;
            RequisitionStatusTypeDescription = billOnlyReviewDAO.RequisitionStatusTypeDescription;
            RequisitionTypeId = billOnlyReviewDAO.RequisitionTypeId;
            RequisitionTypeDescription = billOnlyReviewDAO.RequisitionTypeDescription;
            PatientId = billOnlyReviewDAO.PatientId;
            PatientName = billOnlyReviewDAO.PatientName;
            Provider = billOnlyReviewDAO.Provider;
            LocationIdentifier = billOnlyReviewDAO.LocationIdentifier;
            CreateDate = billOnlyReviewDAO.CreateDate;
            CreatedBy = billOnlyReviewDAO.CreatedBy;
            IsVendor = billOnlyReviewDAO.IsVendor;
            RequisitionSubmissionTypeId = billOnlyReviewDAO.RequisitionSubmissionTypeId;
            RequisitionerFirstName = billOnlyReviewDAO.RequisitionerFirstName;
            RequisitionerLastName = billOnlyReviewDAO.RequisitionerLastName;
            CountryCode = billOnlyReviewDAO.CountryCode;
            ProcedureDate = billOnlyReviewDAO.ProcedureDate;
            RequisitionItems = billOnlyReviewDAO.RequisitionItems?.Select(item => new BillOnlyReviewItemDetailsDTO(item))
                .ToList();
            VProBadgeLog = billOnlyReviewDAO.VProBadgeLog;
            DigitalSignOff = billOnlyReviewDAO.DigitalSignOff;
            DigitalSignOffUser = billOnlyReviewDAO.DigitalSignOffUser;
        }
    }
}