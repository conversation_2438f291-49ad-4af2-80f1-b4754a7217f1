﻿using log4net;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices
{
    /// <summary>
    /// Provides business logic and operations for BillOnlyReview requisitions, including retrieval,
    /// pagination, hydration of requisition items, and preparation of requisition data for printing.
    /// </summary>
    /// <remarks>
    /// This service coordinates with repository and external services to fetch, enrich, and return
    /// Bill Only Review requisition data as required by the application. It supports asynchronous
    /// operations for efficient data processing and logging for error tracking.
    /// </remarks>
    public class BillOnlyReviewService : IBillOnlyReviewService
    {
        private readonly ILog _log = LogManager.GetLogger(typeof(BillOnlyReviewService));

        private readonly IBillOnlyReviewRepository _billOnlyReviewRepository;
        private readonly ISmartItemService _smartItemsService; 
        private readonly IParService _parService;

        /// <summary>
        /// Initializes a new instance of the BillOnlyReviewService class.
        /// </summary>
        /// <param name="billOnlyReviewRepository">
        /// The repository used to access BillOnlyReview requisition data.
        /// </param>
        /// <param name="smartItemService">
        /// The service used to retrieve item details from the smart item system.
        /// </param>
        /// <param name="parService">
        /// The service used to retrieve PAR (Periodic Automatic Replenishment) item information.
        /// </param>
        public BillOnlyReviewService(IBillOnlyReviewRepository billOnlyReviewRepository, ISmartItemService smartItemService, IParService parService)
        {
            _billOnlyReviewRepository = billOnlyReviewRepository;
            _smartItemsService = smartItemService;
            _parService = parService;
        }

        public async Task<PaginatedBORDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                var unpaginatedRequisitionsQuery = await Task.Run(() => _billOnlyReviewRepository.GetBillOnlyReviewRequisitions(request));
                if (unpaginatedRequisitionsQuery == null || !unpaginatedRequisitionsQuery.Any())
                {
                    return new PaginatedBORDTO();
                }

                var paginatedData = unpaginatedRequisitionsQuery
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize);

                var paginatedList = paginatedData.ToList();

                // Simplified hydration call
                await HydrateBillOnlyReviewItems(request.UserName, paginatedList);

                foreach (var requisition in paginatedList)
                {
                    requisition.IsSPR = false; // Default
                    if (requisition.RequisitionItems != null && requisition.RequisitionItems.Any(item => item.SPRDetail != null))
                    {
                        requisition.IsSPR = true;
                    }
                }
                var paginatedResult = new PaginatedBORDTO
                {
                    DisplayedBORRequisitions = paginatedList,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalCount = unpaginatedRequisitionsQuery.Count()
                };

                return paginatedResult;
            }
            catch (Exception ex)
            {
                _log.Error("Error in GetBillOnlyReviewRequisitions: " + ex.Message, ex);
                throw;
            }
        }

        /// <summary>
        /// Retrieves a list of BillOnlyReview requisition details for printing, based on the specified request criteria.
        /// </summary>
        /// <param name="request">
        /// The <see cref="BillOnlyReviewRequest"/> containing filter and selection criteria for the requisitions to print.
        /// </param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result contains a list of BillOnlyReviewDetailsDTO
        /// objects that match the request and are suitable for printing.
        /// </returns>
        public async Task<List<BillOnlyReviewDetailsDTO>> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                List<BillOnlyReviewDAO> returnedRequisitions = await Task.Run(() => _billOnlyReviewRepository.PrintBillOnlyReviewRequisitions(request).ToList());

                List<BillOnlyReviewDetailsDTO> requisitionsToPrint = returnedRequisitions.AsParallel().Select(r => new BillOnlyReviewDetailsDTO(r)).ToList();

                return requisitionsToPrint;
            }
            catch (Exception ex)
            {
                _log.Error("Error getting bill only review requisition for print using request object given in BillOnlyReviewService in RequisitionServices API, Method: BillOnlyReviewService.PrintBillOnlyReviewRequisitions", ex);
                throw;
            }
        }

        private async Task HydrateBillOnlyReviewItems(string userName, List<BillOnlyReviewDTO> billOnlyReviewDTOs)
        {
            if (billOnlyReviewDTOs == null || !billOnlyReviewDTOs.Any())
            {
                return;
            }

            var tasks = billOnlyReviewDTOs.Select(async borDTO =>
            {
                var locationParts = borDTO.LocationIdentifier.Split('_');
                var usernameParts = userName.Split('/');
                string departmentId = locationParts[1];

                var itemTasks = borDTO.RequisitionItems.Select(async item =>
                {
                    // Populate Item object for each item  
                    try
                    {
                        var itemDataFromSmart = await Task.Run(() => _smartItemsService.GetItemByItemId(usernameParts[1], locationParts[0], item.ItemId));
                        item.Item = itemDataFromSmart ?? null;
                    }
                    catch
                    {
                        item.Item = null;
                    }

                    // Populate Par object for each item  
                    try
                    {
                        var parDataList = await Task.Run(() => _parService.GetParItemsByItem(userName, locationParts[0], int.Parse(departmentId), item.ItemId));
                        item.ParItem = parDataList?.FirstOrDefault();
                    }
                    catch
                    {
                        item.ParItem = null;
                    }
                });

                await Task.WhenAll(itemTasks);
            });

            await Task.WhenAll(tasks);
        }
    }
}
