﻿using log4net;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class BillOnlyReviewController : ApiController
    {
        private readonly ILog log = LogManager.GetLogger(typeof(BillOnlyReviewController));

        private IBillOnlyReviewService _billOnlyReviewService;

        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewController"/> class.
        /// </summary>
        /// <param name="billOnlyReviewService">The bill only review service.</param>
        public BillOnlyReviewController(IBillOnlyReviewService billOnlyReviewService)
        {
            _billOnlyReviewService = billOnlyReviewService;
        }

        /// <summary>
        /// Retrieves a list of Bill Only Review Requisitions based on the provided request parameters.
        /// </summary>
        /// <param name="request">The request parameters for retrieving Bill Only Review Requisitions.</param>
        /// <returns>A list of Bill Only Review Requisitions.</returns>
        /// <exception cref="Exception">Thrown when an error occurs while retrieving the requisitions.</exception>
        [HttpPost]
        public async Task<PaginatedBORDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                return await _billOnlyReviewService.GetBillOnlyReviewRequisitions(request);
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Failed to load bill-only requisitions for review. There was an unexpected error with the request object when calling BillOnlyReviewController.GetBillOnlyReviewRequisitions in the RequisitionServices API. Please check the request parameters and API logs for more details."), ex);
                throw;
            }
        }

        /// <summary>
        /// Retrieves a list of Bill Only Review Requisitions based on the provided request parameters.
        /// </summary>
        /// <param name="request">The request parameters for retrieving Bill Only Review Requisitions.</param>
        /// <returns>A list of Bill Only Review Requisitions.</returns>
        /// <exception cref="Exception">Thrown when an error occurs while retrieving the requisitions.</exception>
        [HttpPost]
        public async Task<List<BillOnlyReviewDetailsDTO>> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                var requisitionsToPrint = await _billOnlyReviewService.PrintBillOnlyReviewRequisitions(request);
                return requisitionsToPrint;
            }
            catch (Exception ex)
            {
                log.Error("Error getting bill only review requisitions using request object given in BillOnlyReviewController in RequisitionServices API, Method: BillOnlyReviewController.PrintBillOnlyReviewRequisitions", ex);
                throw;
            }
        }
    }
}
