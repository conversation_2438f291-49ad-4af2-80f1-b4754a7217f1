﻿using eProcurementServices.Domain.DTO.BillOnlyReview;
using System.Collections.Generic;

namespace eProcurementServices.Domain.Model.Reports.MigraDoc
{
    /// <summary>
    /// Represents a display model for a Bill Only Review item, including item, vendor, cost, and requisition details for reporting.
    /// </summary>
    public class BillOnlyReviewItemsDisplay
    {
        //private const string FallbackNoItemId = "No Item ID";
        //private const string FallbackNoDescription = "No Description";
        //private const string FallbackNoParId = "No Par ID";
        //private const string FallbackNoVendorName = "No Vendor Name";
        //private const string FallbackNoReorderNumber = "No Reorder Number";
        //private const string FallbackNoCatalogNumber = "No Catalog Number";
        //private const string FallbackNoProcedureCode = "No Procedure Code";
        //private const string FallbackNoLocation = "No Location";
        //private const string FallbackNoGL = "No GL";
        //private const string FallbackNoUOM = "No UOM";
        //private const string FallbackNA = "N/A";

        public string ItemNumber { get; internal set; }
        public string ItemDescription { get; internal set; }
        public bool IsStock { get; internal set; }
        public string VendorName { get; internal set; }
        public string VendorNumber { get; internal set; }
        public string ParItemLocation { get; internal set; }
        public string ReorderNumber { get; internal set; }
        public string CatalogNumber { get; internal set; }
        public string ProcedureCode { get; internal set; }
        public string Chargeable { get; internal set; }
        public string Par { get; internal set; }
        public string Location { get; internal set; }
        public string Stock { get; internal set; }
        public string Discount { get; internal set; }
        public string Unit { get; internal set; }
        public string Total { get; internal set; }
        public string GL { get; internal set; }
        public string Quantity { get; internal set; }
        public string UOMCode { get; internal set; }
        public IEnumerable<string> Lot { get; internal set; }
        public IEnumerable<string> Serial { get; internal set; }
        public string PONumber { get; internal set; }
        public string ParentSystemId { get; internal set; }
        public string QuantityFulfilled { get; internal set; }
        public string IsRush { get; internal set; }
        public string UpchargeAmount { get; internal set; }
        public string OnContract { get; internal set; }
        public string PartsWarrantyMonths { get; internal set; }
        public string LaborWarrantyMonths { get; internal set; }

        // Make constructor internal to allow use by mapper in same assembly
        internal BillOnlyReviewItemsDisplay() { }

        // Static helper methods for mapping logic
        internal static decimal? UnitCostCalculation(BillOnlyReviewItemDAO reqItem)
        {
            decimal? unitCost = null;
            if (reqItem.VboHoldItemConversion != null && reqItem.VboHoldItemConversion.ItemDetails != null)
            {
                unitCost = reqItem.VboHoldItemConversion.ItemDetails.ParPrice;
            }
            else if (reqItem.SPRDetail != null)
            {
                unitCost = reqItem.SPRDetail.EstimatedPrice;
            }
            else if (reqItem.UnitCost.HasValue)
            {
                unitCost = reqItem.UnitCost.Value;
            }
            if (unitCost.HasValue && reqItem.Discount.HasValue)
            {
                unitCost = unitCost.Value * (100 - reqItem.Discount.Value) / 100;
            }
            return unitCost;
        }

        internal static decimal TotalItemCostCalculation(BillOnlyReviewItemDAO reqItem)
        {
            decimal total = 0m;
            if (reqItem.MainItemId.HasValue)
            {
                total = 0m;
            }
            else if (reqItem.VboHoldItemConversion != null && reqItem?.VboHoldItemConversion.ItemDetails != null)
            {
                decimal unitCost = reqItem.VboHoldItemConversion.UnitCost;
                if (reqItem.Discount.HasValue)
                {
                    unitCost = (unitCost * (100 - reqItem.Discount.Value)) / 100;
                }
                total = unitCost * reqItem.QuantityToOrder;
            }
            else if (reqItem.SPRDetail != null && reqItem.SPRDetail.EstimatedPrice.HasValue)
            {
                decimal unitCost = reqItem.SPRDetail.EstimatedPrice.Value;
                if (reqItem.Discount.HasValue)
                {
                    unitCost = (unitCost * (100 - reqItem.Discount.Value)) / 100;
                }
                total = unitCost * reqItem.QuantityToOrder;
            }
            else if (reqItem.UnitCost.HasValue)
            {
                decimal unitCost = reqItem.UnitCost.Value;
                if (reqItem.Discount.HasValue)
                {
                    unitCost = (unitCost * (100 - reqItem.Discount.Value)) / 100;
                }
                total = unitCost * reqItem.QuantityToOrder;
            }
            return total;
        }

        internal static string OrDefault(string value, string fallback) =>
            string.IsNullOrWhiteSpace(value) ? fallback : value;

        internal static string ToYesNo(bool? val) =>
            val.HasValue ? (val.Value ? "Yes" : "No") : "No";

        internal static string ToStringOrNA(object val) =>
            val?.ToString() ?? "N/A";
    }
}