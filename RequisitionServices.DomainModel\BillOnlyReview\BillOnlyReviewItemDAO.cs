﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    /// <summary>
    /// Data access object representing a Bill Only Review item and its associated details.
    /// </summary>
    public class BillOnlyReviewItemDAO
    {
        /// <summary>
        /// Gets or sets the item identifier.
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// Gets or sets the item description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the reorder number for the item.
        /// </summary>
        public string ReOrderNumber { get; set; }

        /// <summary>
        /// Gets or sets the catalog number for the item.
        /// </summary>
        public string CatalogNumber { get; set; }

        /// <summary>
        /// Gets or sets the main item identifier.
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the discount applied to the item.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the status type identifier.
        /// </summary>
        public int StatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the quantity to order.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the vendor name.
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// Gets or sets the vendor number.
        /// </summary>
        public string VendorNumber { get; set; }

        /// <summary>
        /// Gets or sets the general ledger (GL) code.
        /// </summary>
        public string GL { get; set; }

        /// <summary>
        /// Gets or sets the total cost of the item.
        /// </summary>
        public decimal? TotalCost { get; set; }

        /// <summary>
        /// Gets or sets the unit of measure code.
        /// </summary>
        public string UOMCode { get; set; }

        /// <summary>
        /// Gets or sets the par location.
        /// </summary>
        public string ParLocation { get; set; }

        /// <summary>
        /// Gets or sets the par value.
        /// </summary>
        public string Par { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the item is in stock.
        /// </summary>
        public bool? Stock { get; set; }

        /// <summary>
        /// Gets or sets the associated par item.
        /// </summary>
        public ParItem ParItem { get; set; }

        /// <summary>
        /// Gets or sets the associated item details.
        /// </summary>
        public Item Item { get; set; }

        /// <summary>
        /// Gets or sets the SPR (Special Purchase Request) detail.
        /// </summary>
        public SPRDetailDTO SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the VBO (Vendor Bill Only) hold item conversion details.
        /// </summary>
        public VboHoldItemConversionDto VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the collection of lot numbers.
        /// </summary>
        public IEnumerable<string> Lot { get; set; }

        /// <summary>
        /// Gets or sets the collection of serial numbers.
        /// </summary>
        public IEnumerable<string> Serial { get; set; }
    }
}
