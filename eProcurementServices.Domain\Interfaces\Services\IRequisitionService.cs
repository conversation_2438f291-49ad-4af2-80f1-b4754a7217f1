﻿using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.Model.BillOnlyReview;
using eProcurementServices.Domain.Model.Item;
using eProcurementServices.Domain.Model.Requisition;
using eProcurementServices.Domain.Service;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.BillOnlyReview;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace eProcurementServices.Domain.Interfaces.Services
{
    public interface IRequisitionService
    {
        RequisitionListMultiResultsDTO GetMyRequisitionsResults(RequisitionListMultiRequestDto request);

        ApprovalListMultiResultsDTO GetMyApprovalsResults(RequisitionListMultiRequestDto request);

        ApproverDelegateInfoDTO GetDelegateInfoForApprover(string userName);

        ApprovalListResultsDTO GetUpcomingApprovalsForApprover(RequisitionListRequestDto request);

        List<RequisitionDTO> GetRequisitions(string userName, string COID, DateTime startDate, DateTime endDate, int departmentId);

        IEnumerable<Requisition> GetRequisitionsWithItemStatuses(string userName, string COID, DateTime startDate, DateTime endDate, int departmentId);

        RequisitionListResultsDTO GetRequisitionsByVendor(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetRequisitionsByVendorReportExport(RequisitionReportRequestDto request);
        
        RequisitionListResultsDTO GetVBORequisitionsForReport(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetRequisitionsForReportExport(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetVBORequisitionsForReportExport(RequisitionReportRequestDto request);

        RequisitionListResultsDTO GetRequisitionsForReportByItemNumber(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetRequisitionsForReportByItemNumberExport(RequisitionReportRequestDto request);

        Requisition GetRequisition(int requisitionId, string username);

        EntryExitTypes GetAcquisitionEquipment();

        bool GetParticipatingFacilities(string cOID);

        Requisition GetRequisitionByCoid(int requisitionId, string coid, string username, bool isVendorUser = false);

        Requisition GetRequisitionByLegacyInfo(int requisitionId, string coid, string userName, string countryCode, bool isLegacy);

        Requisition GetRequisition(RequisitionWithDetailsDTO requsitionWithDetails, string username);

        Requisition GetRequisitionAsApprover(int requisitionId, string userName);

        RequisitionItem CreateRequisitionItem(int requisitionId, RequisitionType requisitionType, string cOID, string departmentId, string itemId, string parId, string userName, bool isVendorUser, ApiVersion version = null);

        RequisitionItem ValidateRequisitionItem(RequisitionItem requisitionItem, string userName, RequisitionType requisitionType, string coid , string dept, bool IsVboRequisition, int requisitionId=0, bool isMobile = false, IEnumerable<int> vendorAffiliateIds = null, bool isVboRequisitionInFlight = false);
        
        Requisition SaveRequisition(Requisition requisition, string userName);

        Requisition SaveRequisitionAsApprover(Requisition requisition, string userName);

        Requisition ConvertRequisitionToTemplate(Requisition requisition, string userName);

        Requisition CreateRequisitionFromTemplate(Requisition template, string userName);

        Requisition SubmitRequisition(Requisition requisition, string userName, long? cartId = null);

        StatusUpdateDTO UpdateRequisitionStatus(int requisitionId,string userName, RequisitionStatusType requisitionStatusType, string comments);

        IEnumerable<RequisitionStatusHistory> GetRequisitionHistory(int requisitionId);

        IEnumerable<RequisitionItem> GetParItems(string userName, int requisitionId, string cOID, string departmentId, string parId, bool isVendorUser = false);

        (IEnumerable<RequisitionItem> reqItems, int numFound) GetParItemsWithLastOrderedInfo(ItemSearchCriteria itemSearchCriteria);

        WorkflowValidationDTO ValidateWorkflowForRequisition(string username, WorkflowTypeEnum workflowType, string coid, bool isVendorRequisition, decimal? requisitionTotal = null);

        Requisition ValidateRequisition(Requisition requisition, string userName, bool isVendorUser);

        RequisitionWorkflowDTO GetRequisitionWorkflow(int requisitionId, string userName);

        void DeleteAttachment(FileNamesDTO fileNames);

        bool SmartLegacySubmissionAvailabilityCheck(string userName, string coid);

        InFlightQty GetInFlightQuantity(string userName, string coid, string dept, string parClass, string itemId);

        RequisitionListResultsDTO GetRequisitionsForReport(RequisitionReportRequestDto request);

        /// <summary>
        /// Gest report for purchasin API based on list of COIDs and optional parameters/filters
        /// </summary>
        /// <param name="PurchasingRequisitionReportParameters"></param>
        /// <returns>multiple lists of values</returns>
        PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport(PurchasingRequisitionReportParameters reportParameters);

        /// <summary>
        /// GetAdvancedFiltersForPurchasingReport gets a list of advanced filters for the Purchasing
        /// </summary>
        /// <param name="coidList"></param>
        /// <returns>multiple lists of values</returns>
        RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList);

        /// <summary>
        /// Retrieves a list of requisitions based on the provided purchase order number and COID.
        /// </summary>
        /// <param name="poNumber">The purchase order number.</param>
        /// <param name="coid">The facility ID.</param>
        /// <returns>A list of <see cref="Requisition"/> objects that match the specified criteria.</returns>
        List<Requisition> GetRequisitionAndItemsByPONumber(int poNumber, string coid, string userName);
    }
}
