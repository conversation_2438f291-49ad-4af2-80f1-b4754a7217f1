﻿using System;
using System.Collections.Generic;

namespace eProcurementServices.Domain.DTO
{
    /// <summary>  
    /// Represents a Data Transfer Object for Bill-Only Review.  
    /// </summary>  
    public class BillOnlyReviewDTO
    {
        /// <summary>  
        /// Initializes a new instance of the <see cref="BillOnlyReviewDTO"/> class.  
        /// </summary>  
        public BillOnlyReviewDTO() { }

        /// <summary>  
        /// Gets or sets the unique identifier for the requisition.  
        /// </summary>  
        public int RequisitionId { get; set; }

        /// <summary>  
        /// Gets or sets the status type identifier for the requisition.  
        /// </summary>  
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>  
        /// Gets or sets the description of the requisition status type.  
        /// </summary>  
        public string RequisitionStatusTypeDescription { get; set; }

        /// <summary>  
        /// Gets or sets the unique identifier for the patient.  
        /// </summary>  
        public string PatientId { get; set; }

        /// <summary>  
        /// Gets or sets the name of the patient.  
        /// </summary>  
        public string PatientName { get; set; }

        /// <summary>  
        /// Gets or sets the name of the provider associated with the requisition.  
        /// </summary>  
        public string Provider { get; set; }

        /// <summary>  
        /// Gets or sets the location identifier for the requisition.  
        /// </summary>  
        public string LocationIdentifier { get; set; }

        /// <summary>  
        /// Gets or sets the creation date of the requisition.  
        /// </summary>  
        public DateTime? CreateDate { get; set; }

        /// <summary>  
        /// Gets or sets the type identifier for the requisition.  
        /// </summary>  
        public int RequisitionTypeId { get; set; }

        /// <summary>  
        /// Gets or sets the description of the requisition type.  
        /// </summary>  
        public string RequisitionTypeDescription { get; set; }

        /// <summary>  
        /// Gets or sets the name of the user who created the requisition.  
        /// </summary>  
        public string CreatedBy { get; set; }

        /// <summary>  
        /// Gets or sets a value indicating whether the requisition is associated with a vendor.  
        /// </summary>  
        public bool IsVendor { get; set; }

        /// <summary>  
        /// Gets or sets the submission type identifier for the requisition.  
        /// </summary>  
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>  
        /// Gets or sets the first name of the requisitioner.  
        /// </summary>  
        public string RequisitionerFirstName { get; set; }

        /// <summary>  
        /// Gets or sets the last name of the requisitioner.  
        /// </summary>  
        public string RequisitionerLastName { get; set; }

        /// <summary>  
        /// Gets or sets the name of the facility associated with the requisition.  
        /// </summary>  
        public string FacilityName { get; set; }

        /// <summary>  
        /// Gets or sets the name of the department associated with the requisition.  
        /// </summary>  
        public string DepartmentName { get; set; }

        /// <summary>  
        /// Gets or sets the procedure date for the requisition.  
        /// </summary>  
        public DateTime? ProcedureDate { get; set; }

        /// <summary>  
        /// Gets or sets the country code associated with the requisition.  
        /// </summary>  
        public string CountryCode { get; set; }

        /// <summary>  
        /// Gets or sets the list of items associated with the requisition.  
        /// </summary>  
        public List<BillOnlyReviewItemDTO> RequisitionItems { get; set; }
    }
}
