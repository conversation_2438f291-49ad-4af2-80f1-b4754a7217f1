﻿using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using System.Linq;

namespace RequisitionServices.Repositories
{
    public interface IBillOnlyReviewRepository
    {
        /// <summary>
        /// Retrieves a list of BillOnlyReview requisitions based on the specified parameters.
        /// </summary>
        /// <param name="request">The parameters for the requisitions.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of BillOnlyReview requisitions.</returns>
        IQueryable<BillOnlyReviewDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request);

        /// <summary>
        /// Retrieves a list of BillOnlyReview requisitions for printing based on the specified parameters within the request object.
        /// </summary>
        /// <param name="request">The parameters used to filter and select the BillOnlyReview requisitions to print.</param>
        /// <returns>
        /// An <see cref="IQueryable{BillOnlyReviewDAO}"/> representing the collection of BillOnlyReview requisitions
        /// that match the provided criteria, suitable for further querying or enumeration.
        /// </returns>
        IQueryable<BillOnlyReviewDAO> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request);
    }
}