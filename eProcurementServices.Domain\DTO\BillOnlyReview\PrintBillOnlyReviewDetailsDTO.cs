﻿using System;
using System.Collections.Generic;
using System.Linq;
using eProcurementServices.Domain.Model.DigitalSignOff;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Model.Reports.MigraDoc;
using eProcurementServices.Domain.Model.Requisition;

namespace eProcurementServices.Domain.DTO.BillOnlyReview
{
    /// <summary>
    /// Data Transfer Object for detailed Bill Only Review requisition information, including items, digital sign-off, and vendor badge log.
    /// </summary>
    public class PrintBillOnlyReviewDetailsDTO
    {
        private RequisitionVProBadgeLog _vendorBadgeLog;
        private RequisitionDigitalSignOff _requisitionDigitalSignOff;
        private DigitalSignOffUser _digitalSignOffUser;

        /// <summary>
        /// Gets or sets the requisition ID.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status.
        /// </summary>
        public string RequisitionStatus { get; set; }

        /// <summary>
        /// Gets or sets the requisition type.
        /// </summary>
        public string RequisitionType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is for a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the total number of items in the requisition.
        /// </summary>
        public string TotalItems { get; set; }

        /// <summary>
        /// Gets or sets the total quantity of all items in the requisition.
        /// </summary>
        public string TotalItemQuantities { get; set; }

        /// <summary>
        /// Gets or sets the total amount for the requisition.
        /// </summary>
        public string TotalAmount { get; set; }

        /// <summary>
        /// Gets or sets the company name.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the facility name.
        /// </summary>
        public string FacilityName { get; internal set; }

        /// <summary>
        /// Gets or sets the department name.
        /// </summary>
        public string DepartmentName { get; internal set; }

        /// <summary>
        /// Gets or sets the provider name.
        /// </summary>
        public string ProviderName { get; set; }

        /// <summary>
        /// Gets or sets the patient account number.
        /// </summary>
        public string PatientAccountNumber { get; set; }

        /// <summary>
        /// Gets or sets the patient name.
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// Gets or sets the procedure date.
        /// </summary>
        public DateTime? ProcedureDate { get; set; }

        /// <summary>
        /// Gets or sets the name of the person who created the requisition.
        /// </summary>
        public string RequisitionBy { get; set; }

        /// <summary>
        /// Gets or sets the date and time the requisition was created.
        /// </summary>
        public DateTime RequisitionOn { get; set; }

        /// <summary>
        /// Gets or sets the clinician's title.
        /// </summary>
        public string ClinicianTitle { get; set; }

        /// <summary>
        /// Gets or sets the clinician's first name.
        /// </summary>
        public string ClinicianFirstName { get; set; }

        /// <summary>
        /// Gets or sets the clinician's last name.
        /// </summary>
        public string ClinicianLastName { get; set; }

        /// <summary>
        /// Gets or sets the clinician's full name.
        /// </summary>
        public string ClinicianName { get; set; }

        /// <summary>
        /// Gets or sets the clinician's 3/4 ID.
        /// </summary>
        public string ClinicianThreeFour { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the clinician was verified.
        /// </summary>
        public string ClinicianVerified { get; set; }

        /// <summary>
        /// Gets or sets the date and time the requisition was digitally signed.
        /// </summary>
        public string DigitallySigned { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is an SPR (Special Purchase Request).
        /// </summary>
        public bool IsSPR { get; set; } = false;

        /// <summary>
        /// Gets or sets the vendor badge in log.
        /// </summary>
        public RequisitionVProBadgeLog VendorBadgeInLog
        {
            get => _vendorBadgeLog;
            set
            {
                _vendorBadgeLog = value;
                if (value != null && value.BadgeInStatusId == 1)
                {
                    this.VProBadgedIn = "Yes";
                }
                else if (value != null && value.BadgeInStatusId == 0)
                {
                    this.VProBadgedIn = "No";
                }
                else
                {
                    this.VProBadgedIn = "Unknown";
                }
            }
        }

        /// <summary>
        /// Gets or sets the digital sign off information.
        /// </summary>
        public RequisitionDigitalSignOff DigitalSignOff
        {
            get => _requisitionDigitalSignOff;
            set
            {
                _requisitionDigitalSignOff = value;
                if (value != null)
                {
                    this.ClinicianVerified = value.ADValidated.ToString();
                    this.DigitallySigned = value.CreateDate.ToString("MM/dd/yyyy hh:mm tt");
                }
            }
        }

        /// <summary>
        /// Gets or sets the digital sign off user information.
        /// </summary>
        public DigitalSignOffUser DigitalSignOffUser
        {
            get => _digitalSignOffUser;
            set
            {
                if (value != null)
                {
                    this.ClinicianTitle = value.Title;
                    this.ClinicianFirstName = value.FirstName;
                    this.ClinicianLastName = value.LastName;
                    this.ClinicianThreeFour = value.AccountName;
                }
            }
        }

        /// <summary>
        /// Gets or sets the vendor badge in status as a string.
        /// </summary>
        public string VProBadgedIn { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the collection of requisition items.
        /// </summary>
        public IEnumerable<BillOnlyReviewItemsDisplay> RequisitionItems { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="PrintBillOnlyReviewDetailsDTO"/> class using the specified <see cref="BillOnlyReviewDAO"/>.
        /// Populates the DTO with requisition, patient, provider, digital sign-off, and item details for Bill Only Review reporting.
        /// </summary>
        /// <param name="detailsDTO">The <see cref="BillOnlyReviewDAO"/> containing requisition and related details.</param>
        public PrintBillOnlyReviewDetailsDTO(BillOnlyReviewDAO detailsDTO)
        {
            this.RequisitionId = detailsDTO.RequisitionId;
            this.RequisitionStatus = detailsDTO.RequisitionStatusTypeDescription;
            this.RequisitionType = detailsDTO.RequisitionTypeDescription;
            this.IsVendor = detailsDTO.IsVendor;
            this.TotalItems = detailsDTO.RequisitionItems.Count().ToString();
            this.CompanyName = detailsDTO.CompanyName;
            this.FacilityName = detailsDTO.FacilityName;
            this.DepartmentName = detailsDTO.DepartmentName;
            this.TotalItemQuantities = detailsDTO.RequisitionItems.Sum(x => x.QuantityToOrder).ToString();
            this.ProviderName = detailsDTO.Provider;
            this.PatientAccountNumber = detailsDTO.PatientId;
            this.PatientName = detailsDTO.PatientName;
            this.ProcedureDate = detailsDTO.ProcedureDate;
            this.RequisitionBy = detailsDTO.RequisitionerFirstName + " " + detailsDTO.RequisitionerLastName;
            this.RequisitionOn = detailsDTO.CreateDate;
            this.RequisitionSubmissionTypeId = detailsDTO.RequisitionSubmissionTypeId;
            this.RequisitionItems = detailsDTO.RequisitionItems?.Select(x => BillOnlyReviewItemsDisplayMapper.Map(x)).ToList();
            this.VendorBadgeInLog = detailsDTO.VProBadgeLog;
            this.ClinicianVerified = detailsDTO.DigitalSignOff?.ADValidated.ToString();
            this.DigitallySigned = detailsDTO.DigitalSignOff?.CreateDate.ToString("MM/dd/yyyy");
            this.ClinicianTitle = detailsDTO.DigitalSignOffUser?.Title;
            this.ClinicianThreeFour = detailsDTO.DigitalSignOffUser?.AccountName;
            this.ClinicianName = $"{detailsDTO.DigitalSignOffUser?.FirstName} {detailsDTO.DigitalSignOffUser?.LastName}";
            AssignDisplayProperties(detailsDTO);
        }

        private void AssignDisplayProperties(BillOnlyReviewDAO detailsDTO)
        {
            this.TotalAmount = CalculateTotalRequisitionAmount(detailsDTO).Value.ToString("0.00");
        }

        private decimal? CalculateTotalRequisitionAmount(BillOnlyReviewDAO detailsDTO)
        {
            decimal? total = 0;
            foreach (var item in detailsDTO.RequisitionItems)
            {
                decimal? unitCost;
                if (item.VboHoldItemConversion?.UnitCost != null)
                {
                    unitCost = item.VboHoldItemConversion.UnitCost;
                }
                else if (item.VboHoldItemConversion?.UnitCost == null && item.SPRDetail != null)
                {
                    unitCost = item.SPRDetail.EstimatedPrice;
                }
                else if ((detailsDTO.RequisitionStatusTypeId == 3 || detailsDTO.RequisitionStatusTypeId == 4) && item.MainItemId != null)
                {
                    unitCost = 0;
                }
                else
                {
                    unitCost = item.UnitCost;
                }

                if (item.Discount != null)
                {
                    decimal? discountedUnitCost = unitCost * (100 - item.Discount) / 100;
                    total += (discountedUnitCost * item.QuantityToOrder);
                }
                else
                {
                    total += unitCost * item.QuantityToOrder;
                }
            }
            return total;
        }
    }
}
