using eProcurementServices.Domain.Constants;
using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Interfaces.Utilities;
using eProcurementServices.Domain.Model.ItemInfo;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Service;
using eProcurementServices.Utility.WebAPI;
using log4net;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.Reflection;

namespace eProcurementServices.Domain.Utility
{
    public class CacheProvider : ICacheProvider
    {
        private readonly ICacheManager<object> _cacheManager;

        private readonly string _homeApiEndpoint = ConfigurationManager.AppSettings.Get("HomeAPIUrl");
        private readonly string _reqApiEndpoint = ConfigurationManager.AppSettings.Get("RequisitionAPIUrl");
        private const string itemCachePrefix = "ITEM_COID_REQUISITIONID_";
        private const string coidPrefix = "COID_";
        private const string deptsPrefix = "_Depts";
        private const string deptIdPrefix = "_DeptId_";
        private const string getItemInfoById = "ItemInfo/GetItemInfoById/";
        private const string GetDepartmentMethod = "User/GetDepartment/";
        private const string GetAllDepartmentsMethod = "User/GetAllDepartmentsForCache";
        private const string GetFacilityFromSmartMethod = "COID/GetCOIDAsLocation/";
        private const string DepartmentsByFacilityCacheKeyTemplate = "COID_{0}_Depts";
        private const string GetChildCoidsMethod = "Company/ChildCoids/";
        private const string GetFacilityMethod = "Facility/Get/";
        private const string PostUserDetailsByUsernameList = "users/userDetailsByUsernameList";
        private string reqAPIEndpoint = ConfigurationManager.AppSettings.Get("RequisitionAPIUrl");
        private readonly string _appKey = ConfigurationManager.AppSettings.Get("SecurityAPIKey");
        private readonly string _newSecurityUrl = ConfigurationManager.AppSettings.Get("SecurityAPINewUrl");
        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);
        readonly NameValueCollection featureflagsection = (NameValueCollection)ConfigurationManager.GetSection("featureFlags");

        private string _cacheKey;
        private bool _cacheExists;

        public CacheProvider(ICacheManager<object> cacheMgr)
        {
            this._cacheManager = cacheMgr;
        }

        public Department ProvideCOIDDepartmentInformation(string username, string coid, string departmentID)
        {
            Department department = new Department();
            _cacheKey = "COID_" + coid + "_DeptId_" + departmentID;
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);

            if (_cacheExists)
            {
                department = _cacheManager.GetCache<Department>(_cacheKey);
            }

            if (!_cacheExists || department == null)
            {
                department = ApiUtility.ExecuteApiGetTo<Department>(_reqApiEndpoint, GetDepartmentMethod, new Dictionary<string, string>() {
                                                                                                                    { "userName", username },
                                                                                                                    { "COID", coid },
                                                                                                                    { "departmentId", departmentID }
                                                                                                                    });
                if (department != null)
                {
                    _cacheManager.SetCache(_cacheKey, department, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));
                }
            }
            return department;
        }

        public List<Department> ProvideAllDepartmentsByCoid(string username, string coid)
        {
            List<Department> departments = null;
            var departmentsByFacilityCacheKey = String.Format(DepartmentsByFacilityCacheKeyTemplate, coid);
            _cacheExists = _cacheManager.IsCacheExist(departmentsByFacilityCacheKey);

            if (_cacheExists)
            {
                departments = _cacheManager.GetCache<List<Department>>(departmentsByFacilityCacheKey);
            }

            if (!_cacheExists || departments == null)
            {
                departments = ApiUtility.ExecuteApiGetTo<IEnumerable<Department>>(_reqApiEndpoint, GetAllDepartmentsMethod, new Dictionary<string, string>() {
                                                                                                                    { "userName", username },
                                                                                                                    { "COID", coid  }
                }).ToList();

                if (departments != null)
                {
                    _cacheManager.SetCache(departmentsByFacilityCacheKey, departments, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));
                }
            }
            return departments;
        }

        public UserProfile ProvideUserProfile(string url, string token)
        {
            _cacheKey = "UserProfile" + url;
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);

            Logger.Debug("Cache Exists: " + _cacheExists);

            if (_cacheExists)
            {
                var userProfileCache = _cacheManager.GetCache<UserProfile>(_cacheKey);
                return userProfileCache;
            }

            var userProfile = ApiUtility.ExecuteSecurityTokenApiGetTo<UserProfile>(url, null, token);

            if (userProfile != null)
            {
                if (Convert.ToBoolean(featureflagsection["AutoRemoveApproverFeatureOn"]) == true)
                {
                    _cacheManager.SetCache(_cacheKey, userProfile, Convert.ToDouble(ConfigurationManager.AppSettings.Get("CacheUserProfileExpiryTimer")));
                }
                else
                {
                    _cacheManager.SetCache(_cacheKey, userProfile, 1);
                }
            }

            return userProfile;
        }

        public bool ProvideTokenCache(string token, bool validated)
        {
            _cacheKey = token;
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);

            if (_cacheExists)
                return _cacheExists;

            if (validated)
                _cacheManager.SetCache(_cacheKey, true, .08);

            return validated;
        }

        public bool? ProvideItemStatusCache(string itemId, string userName, string coid, int requisitionId)
        {
            var cacheKey = itemCachePrefix + "_" + itemId + "_" + coid + "_" + requisitionId;
            var cachedItemStatus = _cacheManager.GetCache<bool?>(cacheKey);

            if(cachedItemStatus != null)
            {
                return cachedItemStatus;
            }
            else
            {
                try
                {
                    cachedItemStatus = ApiUtility.ExecuteApiGetTo<List<ItemInfoModel>>(reqAPIEndpoint, getItemInfoById, new Dictionary<string, string>()
                    {
                        { "userName", userName },
                        { "coid", coid },
                        { "itemId", itemId }
                    })?.FirstOrDefault()?.IsActive;

                    //condition needs to happen due to DeduplicatingItemsBeforeSaving calling ValidateRequisitionItem
                    //prior to saving to be able to pass requisitionId to cached item status
                    if (requisitionId != 0)
                    {
                        _cacheManager.SetCache(cacheKey, cachedItemStatus, 0.5);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Info("GetItemInfo Failed " + ex.Message);
                }
            return cachedItemStatus;
            }
        }

        public EntryExitTypes ProvideAcquisitionEquipment(string url, string action, string username, string password)
        {
            EntryExitTypes entExiTypes = new EntryExitTypes();

            _cacheKey = "EntryExitTypes" + url + action + "api/hca3/smartinbound/EquipmentTypes";
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);
            Logger.Debug("Cache Exists: " + _cacheExists);

            var AcquisitionData = ApiUtility.ExecuteApiGetTo<EntryExitAcquisition>(url,
                    action,
                    new Dictionary<string,
                    string>(),
                      username,
                      password);

            var EquipmentData = ApiUtility.ExecuteApiGetTo<EntryExitEquipment>(url,
                    "api/hca3/smartinbound/EquipmentTypes",
                    new Dictionary<string,
                    string>(),
                      username,
                      password);

            EntryExitTypes enttypes = new EntryExitTypes();
            enttypes.acqTypes = AcquisitionData.result;
            enttypes.equTypes = EquipmentData.result;

            if (enttypes != null)
            {
                _cacheManager.SetCache(_cacheKey, enttypes, 6);
            }
            return enttypes;
        }

        public EntryExitParticipatingFacilities ProvideParticipatingFacilities(string url, string action, string username, string password, string cOID)
        {
            _cacheKey = "Facility" + url + action;
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);

            Logger.Debug("Cache Exists: " + _cacheExists);

            if (_cacheExists)
                return _cacheManager.GetCache<EntryExitParticipatingFacilities>(_cacheKey);

            var FacilityData = ApiUtility.ExecuteApiGetTo<EntryExitParticipatingFacilities>(url,
                    "api/hca3/smartinbound/ParticipatingFacilities",
                    new Dictionary<string,
                    string>()
                    { {"Coid", cOID } },
                      username,
                      password);

            if (FacilityData != null)
            {
                _cacheManager.SetCache(_cacheKey, FacilityData, 6);
            }

            return FacilityData;
        }

        public IEnumerable<Facility> ProvideFacilitiesFromSpanOfControl(string hierarchy, string organizationId, string userName)
        {
            IEnumerable<Facility> facilities = null;
            _cacheKey = "Facilities_" + organizationId;
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);

            if (_cacheExists)
                return _cacheManager.GetCache<IEnumerable<Facility>>(_cacheKey);

            facilities = ApiUtility.ExecuteApiGetTo<IEnumerable<Facility>>(_homeApiEndpoint, GetChildCoidsMethod, new Dictionary<string, string>()
                                                                                                            {
                                                                                                                { "Hierarchy", hierarchy },
                                                                                                                { "parent", organizationId }
                                                                                                            });
            if (facilities != null)
            {
                _cacheManager.SetCache(_cacheKey, facilities, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));
            }

            return facilities;
        }

        public Facility ProvideCOIDFacilityInformation(string coid, string username)
        {
            Facility facility = null;
            _cacheKey = "COID_" + coid;
            _cacheExists = _cacheManager.IsCacheExist(_cacheKey);

            if (_cacheExists)
            {
                facility = _cacheManager.GetCache<Facility>(_cacheKey);
            }

            if (!_cacheExists || facility == null)
            {
                var location = ApiUtility.ExecuteApiGetTo<Location>(_reqApiEndpoint, GetFacilityFromSmartMethod, new Dictionary<string, string>() {
                                                                                                { "userName", username },
                                                                                                { "cOID", coid } });
                
                var homeFacility = ApiUtility.ExecuteApiGetTo<Facility>(_homeApiEndpoint, GetFacilityMethod, new Dictionary<string, string>()
                {
                    { "coid", coid }
                });

                facility = (location != null && location.Id != null) ? new Facility(location) : null;
                if(facility!= null && homeFacility != null)
                {
                    facility.CompanyName = homeFacility.CompanyName;
                    facility.CompanyId = homeFacility.CompanyId;
                }
                
                if (facility != null)
                {
                    if (coid == CacheObjects.COIDList.First())
                    {
                        _cacheManager.SetCache(_cacheKey, facility, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")), true);
                    }
                    else
                    {
                        _cacheManager.SetCache(_cacheKey, facility, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));
                    }
                }
            }

            //AddCOIDtoCacheList(coid);

            return facility;
        }

        private void AddCOIDtoCacheList(string COID)
        {
            bool coidExists = CacheObjects.COIDList.Any(x => x == COID);
            if (!coidExists)
            {
                CacheObjects.COIDList.Add(COID);
            }
        }

        public void CacheFacilities(List<Facility> coidList)
        {
            var firstCOIDKey = coidList.First();
            var remainingCOIDKeys = coidList.Skip(1).ToList();

            if (firstCOIDKey != null)
            {
                CacheFacilities(firstCOIDKey, true);
            }
            if (remainingCOIDKeys != null && remainingCOIDKeys.Any())
            {
                remainingCOIDKeys.ForEach(x => { CacheFacilities(x, false); });
            }
        }

        private void CacheFacilities(Facility facility, bool IsFirstKey)
        {
            _cacheKey = "COID_" + facility.COID;
            if (facility != null)
            {
                _cacheManager.SetCache(_cacheKey, facility, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")), IsFirstKey);
            }
        }

        public void CacheDepartmentsbyFacility(List<Facility> coidList)
        {
            List<Department> departments = new List<Department>();
            string username = "System";

            coidList.ForEach(x =>
            {
                var COID_Departments = ApiUtility.ExecuteApiGetTo<IEnumerable<Department>>(_reqApiEndpoint, GetAllDepartmentsMethod, new Dictionary<string, string>() {
                                                                                                                    { "userName", username },
                                                                                                                    { "COID", x.COID  }
                }).ToList();

                var departmentsByFacilityCacheKey = String.Format(DepartmentsByFacilityCacheKeyTemplate, x.COID);
                _cacheManager.SetCache(departmentsByFacilityCacheKey, COID_Departments, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));

                departments.AddRange(COID_Departments);
            });

            departments.ForEach(x =>
            {
                _cacheKey = "COID_" + x.ZeroPaddedCOID + "_DeptId_" + x.Id;
                _cacheManager.SetCache(_cacheKey, x, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));
            });

        }

        public void RemoveCacheByCoidPrefix(string coid)
        {
            var departmentsByFacilityCacheKey = String.Format(DepartmentsByFacilityCacheKeyTemplate, coid);

            var departments = _cacheManager.GetCache<List<Department>>(departmentsByFacilityCacheKey);

            var fullKeylist = new List<string>();

            var facilityKey = $"{coidPrefix}{coid}";
            fullKeylist.Add(facilityKey);

            var facilityDeptPrefixKey = $"{coidPrefix}{coid}{deptsPrefix}";
            fullKeylist.Add(facilityDeptPrefixKey);
            if (departments != null)
            {
            foreach (var department in departments)
                {
                    var departmentId = department.Id;             
                    var fullFacilityCoidKey = $"{coidPrefix}{coid}{deptIdPrefix}{departmentId}";
                    fullKeylist.Add(fullFacilityCoidKey);
                }
            }
            fullKeylist.ForEach(key => _cacheManager.RemoveCache(key));
        }

        public void RemoveCache(string key)
        {
            _cacheManager.RemoveCache(key);
        }

        public void CacheInProgress(string key)
        {
            _cacheManager.SetCache(key, true, Convert.ToInt16(ConfigurationManager.AppSettings.Get("CacheFacilityDeptExpiryTimer")));
        }

        public bool IsCachinginProgress()
        {
            return _cacheManager.IsCacheExist(CacheObjects.CachinginProgressKey);
        }

        public List<UserProfile> ProvideApproverProfileList(List<string> userNameList, string token)
        {
            List<UserProfile> fullApproverProfileListToReturn = new List<UserProfile>();
            try
            {
                // list of profile keys to send to security for a list of user profiles to add to cache
                List<string> profilesToFetchFromSecurity = new List<string>();
                //loop to check cache to see if a key exists, then make a list of keys to send to security
                CheckRedisForApproverProfile(userNameList, fullApproverProfileListToReturn, profilesToFetchFromSecurity);

                if (profilesToFetchFromSecurity.Any())
                {
                    //method to call security to get the user profiles that are not in cache
                    List<UserProfile> fetchedProfilesFromSecurity = GetApproverProfilesFromSecurity(token, profilesToFetchFromSecurity);
                    // if the fetched profiles are null, log an error and initialize to an empty list
                    if (fetchedProfilesFromSecurity == null)
                    {
                        Logger.Info("GetApproverProfilesFromSecurity returned null in ProvideApproverProfileList.");
                        fetchedProfilesFromSecurity = new List<UserProfile>();
                    }
                    SetNewCacheKeysFromSecurity(fullApproverProfileListToReturn, fetchedProfilesFromSecurity);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error in ProvideApproverProfileList", ex);
                fullApproverProfileListToReturn = new List<UserProfile>();
            }
            return fullApproverProfileListToReturn;
        }

        private List<UserProfile> GetApproverProfilesFromSecurity(string token, List<string> profilesToFetchFromSecurity)
        {
            var profiles = new List<UserProfile>();
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    { "userNames", profilesToFetchFromSecurity },
                    { "appKey", _appKey }
                };
                var result = ApiUtility.ExecuteSecurityApiPostTo<List<UserProfile>>(
                    $"{_newSecurityUrl}{PostUserDetailsByUsernameList}", parameters, token);

                if (result == null)
                {
                    Logger.Info("ApiUtility.ExecuteTokenSecurityApiPostTo returned null or an empty list from Security in GetApproverProfilesFromSecurity.");
                }
                else
                {
                    profiles = result;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error in GetApproverProfilesFromSecurity", ex);
            }
            return profiles;
        }

        private void CheckRedisForApproverProfile(List<string> userNameList, List<UserProfile> fullApproverProfileListToReturn, List<string> profilesToFetchFromSecurity)
        {
            try
            {
                foreach (var userName in userNameList)
                {
                    string cacheKeyToCheck = $"UserProfile{_newSecurityUrl}users/{userName.ToLower()}/applications/{_appKey}";
                    if (_cacheManager.IsCacheExist(cacheKeyToCheck))
                    {
                        //if the profile key exists in cache, add it to the list to return
                        UserProfile userProfileFromCache = _cacheManager.GetCache<UserProfile>(cacheKeyToCheck);
                        if (userProfileFromCache != null)
                        {
                            //add the user profile if it exists in cache
                            fullApproverProfileListToReturn.Add(userProfileFromCache);
                        }
                    }
                    else
                    {
                        profilesToFetchFromSecurity.Add(userName);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error in CheckRedisForApproverProfile", ex);
            }
        }

        private void SetNewCacheKeysFromSecurity(List<UserProfile> fullApproverProfileListToReturn, List<UserProfile> fetchedProfiles)
        {
            try
            {
                foreach (var profile in fetchedProfiles)
                {
                    string cacheKeyToSet = $"UserProfile{_newSecurityUrl}users/{profile.UserName.ToLower()}/applications/{_appKey}";
                    _cacheManager.SetCache(cacheKeyToSet, profile, Convert.ToDouble(ConfigurationManager.AppSettings.Get("CacheUserProfileExpiryTimer")));
                    fullApproverProfileListToReturn.Add(profile);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error in SetNewCacheKeysFromSecurity", ex);
            }
        }
    }
}
