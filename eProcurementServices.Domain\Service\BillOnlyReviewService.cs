﻿using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.DTO.BillOnlyReview;
using eProcurementServices.Domain.Interfaces.Services;
using eProcurementServices.Domain.Model.BillOnlyReview;
using eProcurementServices.Domain.Model.Reports.MigraDoc;
using eProcurementServices.Utility.WebAPI;
using log4net;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;
using Smart.Core.Common.Extensions;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using MigraDocTable = MigraDoc.DocumentObjectModel.Tables.Table;
using MigraDocUnit = MigraDoc.DocumentObjectModel.Unit;

namespace eProcurementServices.Domain.Service
{
    public class BillOnlyReviewService : IBillOnlyReviewService
    {
        private readonly ILog log = LogManager.GetLogger(typeof(BillOnlyReviewService));
        private const string billOnlyReviewRequisitionMethod = "BillOnlyReview/GetBillOnlyReviewRequisitions/";
        private const string printOnlyReviewRequisitionMethod = "BillOnlyReview/PrintBillOnlyReviewRequisitions/";
        private string reqAPIEndpoint = ConfigurationManager.AppSettings.Get("RequisitionAPIUrl");
        private string headerImagePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images", "SMART_logo_110.png");

        private IProfileService _profileService;
        private IItemService _itemService;

        public BillOnlyReviewService(IProfileService profileSvc, IItemService itemService)
        {
            _profileService = profileSvc;
            _itemService = itemService;
        }

        /// <summary>
        ///Retrieves bill only review requisitions based on the specified request
        /// </summary>
        /// <param name="request">The request containing search criteria</param>
        /// <returns></returns>
        public async Task<PaginatedBORResult> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                var facilitiesDictionary = request.Facilities.ToDictionary(x => x.COID, x => x.Name);
                var departmentDictionary = request.Facilities
                    .SelectMany(x => _profileService.GetAllDepartmentsFromCache(request.UserName, x.COID)).ToDictionary(x => $"{x.ZeroPaddedCOID}_{x.Id}", x => x.Description);


                var matchedLocations = new List<string>();
                if (!string.IsNullOrWhiteSpace(request.RequisitionSearch))
                {
                    var matchedLocationIdentifierSet = new HashSet<string>();

                    foreach (var Department in facilitiesDictionary)
                    {
                        if (Department.Value.IndexOf(request.RequisitionSearch, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            string facilityId = Department.Key;
                            string facilityPrefix = facilityId + "_";

                            foreach (var department in departmentDictionary)
                            {
                                if (department.Key.StartsWith(facilityPrefix))
                                {
                                    matchedLocationIdentifierSet.Add(department.Key);
                                }
                            }
                        }
                    }

                    foreach (var department in departmentDictionary)
                    {
                        if (department.Value.IndexOf(request.RequisitionSearch, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            matchedLocationIdentifierSet.Add(department.Key);
                        }
                    }

                    foreach (var identifier in matchedLocationIdentifierSet)
                    {
                        matchedLocations.Add(identifier);
                    }
                }
                request.Locations = matchedLocations;


                var billOnlyReviewPageResponse = await ApiUtility.ExecuteApiPostToAsync<PaginatedBORResult>(reqAPIEndpoint, billOnlyReviewRequisitionMethod, null, request.ToJsonContent());
                if (billOnlyReviewPageResponse == null || billOnlyReviewPageResponse.DisplayedBORRequisitions == null)
                {
                    return new PaginatedBORResult();
                }

                foreach (var billOnlyReviewSearchResult in billOnlyReviewPageResponse.DisplayedBORRequisitions)
                {
                    if (!string.IsNullOrWhiteSpace(billOnlyReviewSearchResult.LocationIdentifier))
                    {
                        var locationPieces = billOnlyReviewSearchResult.LocationIdentifier.Split('_');
                        if (locationPieces.Length == 2)
                        {
                            billOnlyReviewSearchResult.FacilityName = facilitiesDictionary.TryGetValue(locationPieces.First(), out var facility)
                                ? $"{facility} ({locationPieces.First()})"
                                : locationPieces.First();

                            billOnlyReviewSearchResult.DepartmentName = departmentDictionary.TryGetValue(billOnlyReviewSearchResult.LocationIdentifier, out var department)
                                ? $"{department} ({locationPieces.Last()})"
                                : locationPieces.Last();
                        }
                    }
                    
                    var totalAmount = billOnlyReviewSearchResult.TotalRequisitionAmount;
                }
                
                return billOnlyReviewPageResponse;
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error getting bill only review requisition search results using patientId given in BillOnlyReviewService in eProcurementServices API, Method: BillOnlyReviewService.GetBillOnlyReviewRequisitions"), ex);
                throw;
            }
        }      

        /// <summary>
        /// Generates a simple PDF document indicating that no results were found.
        /// </summary>
        /// <returns>A byte array containing the PDF data.</returns>
        private byte[] GenerateNoResultsPdf(string message)
        {
            Document document = new Document();
            Section section = document.AddSection();
            Paragraph para = section.AddParagraph(message);
            para.Format.Alignment = ParagraphAlignment.Center;
            para.Format.Font.Bold = true;

            PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer();
            pdfRenderer.Document = document;
            pdfRenderer.RenderDocument();

            using (MemoryStream stream = new MemoryStream())
            {
                pdfRenderer.PdfDocument.Save(stream, false);
                return stream.ToArray();
            }
        }

        public async Task<byte[]> PrintBillOnlyReviewRequisitions(BillOnlyReviewRequest request) // Keep signature consistent
        {
            try 
            {
                var returnedRequisitionDetails = await ApiUtility.ExecuteApiPostToAsync<List<BillOnlyReviewDAO>>(reqAPIEndpoint, printOnlyReviewRequisitionMethod, null, request.ToJsonContent());

                if (returnedRequisitionDetails == null || !returnedRequisitionDetails.Any())
                {
                    log.Warn("No requisition found to generate PDF.");
                    return GenerateNoResultsPdf("No requisition found to print.");
                }
                GetMissingItemAndParItemDetails(request, returnedRequisitionDetails);

                bool isFirstRequisitionToPrint = true;

                var facilitiesDictionary = request.Facilities.ToDictionary(x => x.COID, x => x.Name);
                var companyDictionary = request.Facilities.ToDictionary(x => x.COID, x => x.CompanyName);

                var departmentDictionary = request.Facilities
                    .SelectMany(x => _profileService.GetAllDepartmentsFromCache(request.UserName, x.COID)).ToDictionary(x => $"{x.ZeroPaddedCOID}_{x.Id}", x => x.Description);
                foreach (var requisition in returnedRequisitionDetails)
                {
                    if (!string.IsNullOrWhiteSpace(requisition.LocationIdentifier))
                    {
                        var locationPieces = requisition.LocationIdentifier.Split('_');
                        if (locationPieces.Length == 2)
                        {
                            requisition.FacilityName = facilitiesDictionary.TryGetValue(locationPieces.First(), out var facility)
                                ? $"{facility} ({locationPieces.First()})"
                                : locationPieces.First();

                            requisition.CompanyName = companyDictionary.TryGetValue(locationPieces.First(), out var company)
                                ? $"{company}"
                                : locationPieces.First();

                            requisition.DepartmentName = departmentDictionary.TryGetValue(requisition.LocationIdentifier, out var department)
                                ? $"{department} ({locationPieces.Last()})"
                                : locationPieces.Last();
                        }
                    }
                }
                var formattedRequisitionsToPrint = returnedRequisitionDetails.Select(r => new PrintBillOnlyReviewDetailsDTO(r)).ToList();

                // 1. Create MigraDoc document object using full name
                Document migraDoc = new Document();
                migraDoc.Info.Title = "Bill Only Review ";

                // 2. Add Section and Content (Paragraphs)
                Section contentSection = migraDoc.AddSection();
                contentSection.PageSetup.TopMargin = MigraDocUnit.FromInch(0.75); // Reduced top margin
                contentSection.PageSetup.LeftMargin = MigraDocUnit.FromInch(0.18);
                contentSection.PageSetup.RightMargin = MigraDocUnit.FromInch(0.18);
                contentSection.PageSetup.BottomMargin = MigraDocUnit.FromInch(0.75);
                contentSection.PageSetup.HeaderDistance = MigraDocUnit.FromInch(0.25);
                contentSection.PageSetup.FooterDistance = MigraDocUnit.FromInch(0.25);
                contentSection.PageSetup.OddAndEvenPagesHeaderFooter = false;
                contentSection.PageSetup.StartingNumber = 1;


                //Paragraph headerParagraph = new Paragraph();
                if (File.Exists(headerImagePath))
                {
                    MigraDoc.DocumentObjectModel.Shapes.Image image = contentSection.Headers.Primary.AddImage(headerImagePath);
                    image.Width = MigraDocUnit.FromInch(3);
                    image.RelativeVertical = MigraDoc.DocumentObjectModel.Shapes.RelativeVertical.Page;
                    image.RelativeHorizontal = MigraDoc.DocumentObjectModel.Shapes.RelativeHorizontal.Page;
                    image.WrapFormat.Style = MigraDoc.DocumentObjectModel.Shapes.WrapStyle.Through;
                }
                else
                {
                    log.Error($"Header image not found at path: {headerImagePath}");
                }
                contentSection.PageSetup.TopMargin = MigraDocUnit.FromInch(1);

                foreach (var requisition in formattedRequisitionsToPrint)
                {
                    if (!isFirstRequisitionToPrint)
                    {
                        contentSection.AddPageBreak();  
                    }
                    isFirstRequisitionToPrint = false;

                    CreateSummarySection(contentSection, requisition);

                    CreateRequisitionsItemsSection(contentSection, requisition.RequisitionItems);
                }

                Paragraph pageNumberFooter = new Paragraph();
                pageNumberFooter.Format.Font.Name = "Arial";
                pageNumberFooter.Format.Font.Size = MigraDocUnit.FromPoint(10);
                pageNumberFooter.Format.Alignment = ParagraphAlignment.Right;
                pageNumberFooter.AddText("Page ");
                pageNumberFooter.AddPageField();
                pageNumberFooter.AddText(" / ");
                pageNumberFooter.AddNumPagesField();

                contentSection.Footers.FirstPage.Add(pageNumberFooter.Clone());
                contentSection.Footers.Primary.Add(pageNumberFooter.Clone());

                PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer();

                pdfRenderer.Document = migraDoc;
                pdfRenderer.RenderDocument();
                    log.Info("DEBUG: Finished pdfRenderer.RenderDocument().");

                // 6. Save the RENDERER's output to MemoryStream using full name
                // Use System.IO.MemoryStream
                using (MemoryStream stream = new MemoryStream())
                {
                    log.Info("DEBUG: Saving rendered PDF to MemoryStream.");

                    pdfRenderer.PdfDocument.Save(stream, false); // Use leaveOpen: false

                    log.Info($"DEBUG: Minimal MigraDoc test saved. Stream length: {stream.Length}");
                    if (stream.Length < 500) // Text PDFs are usually larger
                    {
                        log.Warn($"DEBUG: Stream length ({stream.Length}) seems small for a text PDF!");
                    }

                    // 7. Return bytes - stream.ToArray() is simplest
                    return stream.ToArray();
                    // The complex buffer reading is not necessary
                }
            }
            catch (System.Exception ex) // Use System.Exception
            {
                log.Error($"Error during minimal MigraDoc TEXT test (v1.50 GDI)", ex);
                throw;
            }
        }

        private void CreateSummarySection(Section section, PrintBillOnlyReviewDetailsDTO requisition)
        {
            MigraDocTable summaryTable = section.AddTable();
            summaryTable.Borders.Visible = false;
            summaryTable.Shading.Color = new Color((byte)243, (byte)243, (byte)243);
            Column summary = summaryTable.AddColumn(MigraDocUnit.FromInch(4));
            Column digitalSignOff = summaryTable.AddColumn(MigraDocUnit.FromInch(4));

            Row headerRow = summaryTable.AddRow();
            headerRow.Cells[0].AddParagraph("Summary");
            headerRow.Cells[0].Format.Alignment = ParagraphAlignment.Center;
            headerRow.Cells[0].Format.Font.Bold = true;
            if(requisition.RequisitionSubmissionTypeId == 1)
            {
                headerRow.Cells[1].AddParagraph("Digital Sign Off");
            }
            headerRow.Cells[1].Format.Alignment = ParagraphAlignment.Center;
            headerRow.Cells[1].Format.Font.Bold = true;

            Row summaryTableValues = summaryTable.AddRow();

            // Add summary section values in the first cell of the second row

            Paragraph summaryDetails = summaryTableValues.Cells[0].AddParagraph();
            summaryDetails.AddText($"\n");

            AddFormattedText(summaryDetails, "Requisition #: ", $"{requisition.RequisitionId}");
            AddFormattedText(summaryDetails, "Requisition Status: ", $"{requisition.RequisitionStatus}");
            AddFormattedText(summaryDetails, "Requisition Type: ", $"{requisition.RequisitionType}");
            AddFormattedText(summaryDetails, "Total Items: ", $"{requisition.TotalItems}");
            AddFormattedText(summaryDetails, "Total Quantities: ", $"{requisition.TotalItemQuantities}");
            AddFormattedText(summaryDetails, "Total Amount: ", $"${requisition.TotalAmount}");
            AddFormattedText(summaryDetails, "Company: ", $"{requisition.CompanyName}");
            AddFormattedText(summaryDetails, "Facility: ", $"{requisition.FacilityName}");
            AddFormattedText(summaryDetails, "Department: ", $"{requisition.DepartmentName}");

            AddFormattedText(summaryDetails, "Provider: ", $"{requisition.ProviderName}");
            AddFormattedText(summaryDetails, "Patient Account #: ", $"{requisition.PatientAccountNumber}");
            AddFormattedText(summaryDetails, "Patient Name: ", $"{requisition.PatientName}");
            AddFormattedText(summaryDetails, "Procedure Date: ", requisition.ProcedureDate.HasValue 
                ? $"{requisition.ProcedureDate.Value.ToString("MM/dd/yyyy")}" : string.Empty);

            AddFormattedText(summaryDetails, "Requisition By: ", $"{requisition.RequisitionBy}");
            //the next if/ else maintains the bottom spacing using new line based on the type of requisition and what the last line will be
            if (requisition.IsVendor)
            {
                AddFormattedText(summaryDetails, "Requisition On: ", $"{requisition.RequisitionOn.ToString("MM/dd/yyyy hh:mm tt")}");
                AddFormattedText(summaryDetails, "Vendor Badge In: ", $"{requisition.VProBadgedIn}\n");
            }
            else
            {
                AddFormattedText(summaryDetails, "Requisition On: ", $"{requisition.RequisitionOn.ToString("MM/dd/yyyy hh:mm tt")}\n");
            }

            if (requisition.RequisitionSubmissionTypeId == 1)
            {
                Paragraph digitalSignOffDetails = summaryTableValues.Cells[1].AddParagraph();
                digitalSignOffDetails.AddText($"\n");

                AddFormattedText(digitalSignOffDetails, "Clinician Title: ", $"{requisition.ClinicianTitle}");
                AddFormattedText(digitalSignOffDetails, "Clinician Name: ", $"{requisition.ClinicianName}");
                AddFormattedText(digitalSignOffDetails, "Clinician 3/4: ", $"{requisition.ClinicianThreeFour}");
                AddFormattedText(digitalSignOffDetails, "Clinician Verified: ", $"{requisition.ClinicianVerified}");
                AddFormattedText(digitalSignOffDetails, "Digitally Signed: ", $"{requisition.DigitallySigned}");
            }

        }

        private void CreateRequisitionsItemsSection(Section section, IEnumerable<BillOnlyReviewItemsDisplay> requisitionItems)
        {
            if (requisitionItems == null || !requisitionItems.Any())
            {
                return;
            }

            // Add a table to the section
            MigraDocTable itemsTable = section.AddTable();

            // Add columns to the table
            itemsTable.AddColumn(MigraDocUnit.FromInch(3)); // Column 1
            itemsTable.AddColumn(MigraDocUnit.FromInch(1.5)); // Column 2
            itemsTable.AddColumn(MigraDocUnit.FromInch(1.25)); // Column 3
            itemsTable.AddColumn(MigraDocUnit.FromInch(1.25)); // Column 4
            itemsTable.AddColumn(MigraDocUnit.FromInch(1)); // Column 5
            int index = 0;
            foreach(var item in requisitionItems) {
                // Add the first row with dummy text
                Row itemHeader = itemsTable.AddRow();
                itemHeader.Borders.Visible = true;
                itemHeader.Cells[0].MergeRight = 4; // Merge all columns
                itemHeader.Cells[0].AddParagraph($"{item.ItemNumber}  -  {item.ItemDescription}");
                itemHeader.Cells[0].Format.Alignment = ParagraphAlignment.Left;

                // Add the second row with dummy text
                Row itemInformation = itemsTable.AddRow();
                itemInformation.Cells[0].Borders.Left.Width = 0.5;
                itemInformation.Cells[4].Borders.Right.Width = 0.5;
                itemInformation.Borders.Bottom.Width = 0.5;


                itemInformation.Cells[0].AddParagraph($"Vendor: {item.VendorName}");
                itemInformation.Cells[0].AddParagraph($"Reorder #: {item.ReorderNumber}");
                itemInformation.Cells[0].AddParagraph($"Catalog #: {item.CatalogNumber}");
                if (item.ProcedureCode != null)
                {
                    itemInformation.Cells[0].AddParagraph($"Procedure Code: {item.ProcedureCode}");
                }
                if(item.Chargeable != null)
                {
                    itemInformation.Cells[0].AddParagraph($"Chargeable: {item.Chargeable}");
                }
                // Project Number - only show for SPR items when VboHoldItemConversion is null
                if (item.ProjectNumber != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"Project #: {item.ProjectNumber}");
                }
                if (item.PONumber != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"PO Number: {item.PONumber}");
                }
                if (item.ParentSystemId != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"SMART REQ #: {item.ParentSystemId}");
                }
                // Quantity Filled - only show for specific status types and conditions (matching frontend logic)
                if (item.QuantityFulfilled != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"Filled: {item.QuantityFulfilled}");
                }
                // Rush - only show when true (matching frontend: ng-if="readOnly && item.IsRushOrder")
                if (item.IsRush == "Yes")
                {
                    itemInformation.Cells[0].AddParagraph($"Rush: {item.IsRush}");
                }
                // Upcharge - only show when amount > 0 (matching frontend logic)
                if (item.UpchargeAmount != "0.00")
                {
                    itemInformation.Cells[0].AddParagraph($"Upcharge: ${item.UpchargeAmount}");
                }
                if (item.OnContract != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"On Contract: {item.OnContract}");
                }
                // Warranty fields - matching frontend feature flag and acquisition type conditions
                if (item.PartsWarrantyMonths != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"Parts Warranty: {item.PartsWarrantyMonths} months");
                }
                if (item.LaborWarrantyMonths != "N/A")
                {
                    itemInformation.Cells[0].AddParagraph($"Labor Warranty: {item.LaborWarrantyMonths} months");
                }
                if (item.Lot?.Any(l => l != null) == true)
                {
                    itemInformation.Cells[0].AddParagraph($"Lot: {string.Join(", ", item.Lot.Where(l => l != null))}");
                }
                if(item.Serial?.Any(s => s != null) == true)
                {
                    itemInformation.Cells[0].AddParagraph($"Serial: {string.Join(", ", item.Serial.Where(s => s != null))}");
                }
                itemInformation.Cells[1].AddParagraph($"Par: {item.Par}");
                itemInformation.Cells[1].AddParagraph($"Location: {item.Location}");
                itemInformation.Cells[1].AddParagraph($"Stock: {item.Stock}");
                if(item.Discount != "0.00")
                {
                    itemInformation.Cells[1].AddParagraph($"Discount: {item.Discount}%");
                }
                itemInformation.Cells[2].AddParagraph($"Unit: ${item.Unit}");
                itemInformation.Cells[2].AddParagraph($"Total: ${item.Total}");
                itemInformation.Cells[2].AddParagraph($"GL: {item.GL}");

                itemInformation.Cells[3].AddParagraph($"UOM: {item.UOMCode}");
                itemInformation.Cells[4].AddParagraph($"QTY: {item.Quantity}");

                index++;
            }
        }

        private void AddFormattedText(Paragraph paragraph, string label, string value = "")
        {
            {
                paragraph.AddFormattedText(label, TextFormat.Bold);
                paragraph.AddText(value);
                paragraph.AddLineBreak();
            }
        }
        private void GetMissingItemAndParItemDetails(BillOnlyReviewRequest request, List<BillOnlyReviewDAO> returnedRequisitions)
        {
            foreach (var tuple in returnedRequisitions.SelectMany(r => r.RequisitionItems.Select(i => (requisition: r, item: i))))
            {
                var locationPieces = tuple.requisition.LocationIdentifier.Split('_');
                var item = tuple.item;

                if (int.TryParse(item.ItemId, out int _))
                {
                    item.Item = _itemService.GetItemByItemId(request.UserName, locationPieces.First(), item.ItemId);
                    if (item.SPRDetail == null)
                    {
                        var parItems = _itemService.GetAvailableParItems(request.UserName, locationPieces.First(), int.Parse(locationPieces.Last()), item.Par, item.ItemId);
                        item.ParItem = parItems.FirstOrDefault();
                    }
                }
                if (item.VboHoldItemConversion != null && item.VboHoldItemConversion.ItemDetails == null)
                {
                    string smartItemNumber = item.VboHoldItemConversion.SmartItemNumber.ToString();
                    var parItems = _itemService.GetAvailableParItems(request.UserName, locationPieces.First(), int.Parse(locationPieces.Last()), item.Par, smartItemNumber);
                    item.VboHoldItemConversion.ItemDetails = parItems.FirstOrDefault();
                }
            }
        }
    }
}
