﻿using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.VPro;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    /// <summary>
    /// Represents a DTO for Bill Only Review Requisition with details.
    /// </summary>
    public class BillOnlyReviewRequisitionWithDetailsDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewRequisitionWithDetailsDTO"/> class.
        /// </summary>
        public BillOnlyReviewRequisitionWithDetailsDTO() { }

        /// </summary>
        /// <param name="requisitionWithDetailsDTO"></param>
        /// <param name="billOnlyReviewRequisition"></param>
        public BillOnlyReviewRequisitionWithDetailsDTO(RequisitionWithDetailsDTO requisitionWithDetailsDTO, BillOnlyReviewRequisition billOnlyReviewRequisition)
        {
            this.RequisitionId = billOnlyReviewRequisition.RequisitionId;
            this.RequisitionStatusTypeId = billOnlyReviewRequisition.RequisitionStatusTypeId;
            this.RequisitionTypeId = billOnlyReviewRequisition.RequisitionTypeId;
            this.RequisitionStatusTypeDescription = billOnlyReviewRequisition.RequisitionStatusTypeDescription;
            this.RequisitionTypeDescription = billOnlyReviewRequisition.RequisitionTypeDescription;
            this.Comments = billOnlyReviewRequisition.Comments;
            this.LocationIdentifier = billOnlyReviewRequisition.LocationIdentifier;
            this.RequisitionParClass = billOnlyReviewRequisition.RequisitionParClass;
            this.CreateDate = billOnlyReviewRequisition.CreateDate;
            this.CreatedBy = billOnlyReviewRequisition.CreatedBy;
            this.CountryCode = billOnlyReviewRequisition.CountryCode;
            this.IsVendor = billOnlyReviewRequisition.IsVendor;
            this.TotalReqAmount = billOnlyReviewRequisition.TotalReqAmount;
            this.RequisitionItems = requisitionWithDetailsDTO.RequisitionItems;
            this.DigitalSignOffUser = billOnlyReviewRequisition.DigitalSignOffUser;
            this.RequisitionSubmissionTypeId = billOnlyReviewRequisition.RequisitionSubmissionTypeId;
            this.User = billOnlyReviewRequisition.User;

            this.RequisitionDigitalSignOff = billOnlyReviewRequisition.RequisitionDigitalSignOff
                .Where(dso => !dso.IsDeleted)
                .FirstOrDefault();
        }

        /// <summary>
        /// Gets or sets the requisition ID.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status type ID.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status type description.
        /// </summary>
        public string RequisitionStatusTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the requisition type description.
        /// </summary>
        public string RequisitionTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the requisition type ID.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the comments.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the location identifier.
        /// </summary>
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the requisition par class.
        /// </summary>
        public string RequisitionParClass { get; set; }

        /// <summary>
        /// Gets or sets the create date.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the created by.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the country code.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether it is vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the total requisition amount.
        /// </summary>
        public decimal? TotalReqAmount { get; set; }

        /// <summary>
        /// Gets or sets the VPRO badge in ID.
        /// </summary>
        public int? BadgeLogId { get; set; }

        /// <summary>
        /// Gets or sets the requisition items.
        /// </summary>
        public IEnumerable<RequisitionItemWithDetailsDTO> RequisitionItems { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition digital sign off.
        /// </summary>
        public RequisitionDigitalSignOff RequisitionDigitalSignOff { get; set; }

        /// <summary>
        /// Gets or sets the digital sign off user.
        /// </summary>
        public DigitalSignOffUser DigitalSignOffUser { get; set; }

        /// <summary>
        /// Gets or sets the user.
        /// </summary>
        public User User { get; set; }

        /// <summary>
        /// Gets or sets the VProBadgeInDetails object
        /// </summary>
        public RequisitionVProBadgeLog VProBadgeLog { get; set; }
    }
}
