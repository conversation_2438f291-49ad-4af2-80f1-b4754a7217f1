﻿using eProcurementServices.Domain.Constants;
using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.Interfaces.Services;
using eProcurementServices.Domain.Interfaces.Utilities;
using eProcurementServices.Domain.Model.FacilityWorkflow;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Utility;
using eProcurementServices.Utility.Model;
using eProcurementServices.Utility.WebAPI;
using log4net;
using Smart.Core.Common.Extensions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;

namespace eProcurementServices.Domain.Service
{
    public class FacilityWorkflowService : IFacilityWorkflowService
    {
        private readonly string _api = ConfigurationManager.AppSettings.Get("RequisitionAPIUrl");
        private const string FacilityWorkflow = "FacilityWorkflow";

        private readonly IUserService _userService;
        private readonly ICOIDService _coidService;
        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public FacilityWorkflowService(IUserService userService, ICOIDService coidService)
        {
            _userService = userService;
            _coidService = coidService;
        }

        public FacilityWorkflowDTO Get(string coid, WorkflowTypeEnum workflowType)
        {
            var parameters = new Dictionary<string, string>
            {
                {"coid", coid },
                {"workflowType", ((int)workflowType).ToString() }
            };

            var workflow = ApiUtility.ExecuteApiGetTo<FacilityWorkflowDTO>(_api, FacilityWorkflow, parameters);

            foreach (var userWorkflowStep in workflow.Steps)
            {
                if (userWorkflowStep.Approver?.User != null)
                {
                    userWorkflowStep.Approver.User.UserProfile = _userService.GetUserProfile(userWorkflowStep.Approver.User.AccountName);
                }
            }

            return workflow;
        }

        public WorkflowValidationDTO Save(SaveFacilityWorkflowDTO request)
        {
            var validation = new WorkflowValidationDTO()
            {
                IsValid = false,
                WorkflowTypeId = (int)WorkflowTypeEnum.Vendor
            };

            if (request.Workflow?.Steps == null || !request.Workflow.Steps.Any())
            {
                validation.IsValid = true;
            }
            else
            {
                validation = Validate(request.Workflow);
            }

            if (validation.IsValid)
            {
                ApiUtility.ExecuteApiPostTo(_api, FacilityWorkflow, null, request.ToJsonContent());
            }

            return validation;
        }

        public WorkflowValidationDTO Validate(FacilityWorkflowDTO workflow)
        {
            var result = new WorkflowValidationDTO(WorkflowTypeEnum.Vendor);

            if (workflow?.Steps == null || !workflow.Steps.Any())
            {
                result.IsValid = false;
                result.WarningMessages.Add(WorkflowValidationWarnings.NoWorkflowSetup);
                return result;
            }

            if (workflow.Steps.Any(x => x.Approver?.User == null))
            {
                result.IsValid = false;
                result.WarningMessages.Add(WorkflowValidationWarnings.UserInfoUnavailable);
                return result;
            }

            foreach (var step in workflow.Steps)
            {
                var newProfile = step.Approver.User.UserProfile == null ?
                    _userService.GetUserProfile(step.Approver.User.AccountName) :
                    _userService.GetUserProfile(step.Approver.User.UserProfile.Domain, step.Approver.User.UserProfile.UserName);

                step.Approver.User.UserProfile = newProfile;
            }

            if (workflow.Steps.Any(x => x.Approver.User.UserProfile != null && x.Approver.User.UserProfile.IsDeactivated))
            {
                result.IsValid = false;
                result.WarningMessages.Add(WorkflowValidationWarnings.DeactivatedApprover);
            }

            if (workflow.Steps.Any(x => x.Approver.User.UserProfile == null))
            {
                result.IsValid = false;
                result.WarningMessages.Add(WorkflowValidationWarnings.UserAccountDoesNotExist);
            }

            var approverAdGroupName = PassUtility.ProcurementAdGroups.First(x => string.Equals(x.Value, EnumUtility.GetEnumDescription(PassRole.Approver), StringComparison.InvariantCultureIgnoreCase)).Key;

            //if (workflow.Steps.Any(x => x.Approver.User.UserProfile != null && x.Approver.User.UserProfile.Roles.All(y => !string.Equals(y.AdGroupName, approverAdGroupName, StringComparison.InvariantCultureIgnoreCase))))
            if (workflow.Steps.Any(x => x.Approver.User.UserProfile != null && x.Approver.User.UserProfile.Roles.All(y => !string.Equals(y.RoleName, UserProfileRoleNames.EProcApproverRoleName, StringComparison.InvariantCultureIgnoreCase))))
            {
                result.IsValid = false;
                result.WarningMessages.Add(WorkflowValidationWarnings.NoApprovalAccess);
            }

            var hierarchy = _coidService.GetHierarchyForCoid(workflow.Coid);

            if (workflow.Steps.Any(x => x.Approver?.User?.UserProfile != null && !HasSpanOfControl(hierarchy, x.Approver.User.UserProfile.SpanOfControl, workflow.Coid)))
            {
                result.IsValid = false;
                result.WarningMessages.Add(WorkflowValidationWarnings.SpanOfControlMissing);
            }

            return result;
        }

        public WorkflowValidationDTO ValidateAndSaveFacilityWorkflow(SaveFacilityWorkflowDTO request)
        {
            SpanOfControlHierarchy hierarchy = _coidService.GetHierarchyForCoid(request.Workflow.Coid);
            IEnumerable<FacilityWorkflowStep> originalStep = request.Workflow.Steps;
            IEnumerable<FacilityWorkflowStep> originalSteps = request.Workflow.Steps;

            //Gets list of user profiles from cache
            List<UserProfile> userProfileList = GetApproverProfileListForFacilityWorkflow(originalSteps);

            IEnumerable<FacilityWorkflowStep> updatedOriginalSteps = UpdateOriginalFacilityWorkflowSteps(originalSteps, userProfileList);
                        
            List<FacilityWorkflowStep> filteredSteps = FilterInvalidFacilityWorkflowSteps(hierarchy, request.Workflow.Coid, updatedOriginalSteps);

            var areEqual = updatedOriginalSteps.OrderBy(s => s.Id).Select(s => new { s.Id }).SequenceEqual(filteredSteps.OrderBy(s => s.Id).Select(s => new { s.Id }));
            if (!areEqual)
            {
                request = new SaveFacilityWorkflowDTO
                {
                    Workflow = new FacilityWorkflowDTO
                    {
                        Steps = filteredSteps,
                        WorkflowTypeId = request.Workflow.WorkflowTypeId,
                        Coid = request.Workflow.Coid
                    }
                };
                //this Save method calls Validate first and then saves returning workflow validations
                return Save(request);
            }
            else
            {
                return Validate(request.Workflow);
            }
        }

        public List<UserProfile> GetApproverProfileListForFacilityWorkflow(IEnumerable<FacilityWorkflowStep> steps)
        {
            try
            {
                string token = _userService.AddSecurityToken();
                ICacheManager<Object> cacheManager = new RedisCacheManager<object>();
                ICacheProvider cacheProvider = new CacheProvider(cacheManager);
                List<UserProfile> approverProfileList = new List<UserProfile>();
                List<string> userNameList = new List<string>();
                foreach (var userName in steps.Select(x => x.Approver?.User?.AccountName).Where(x => !string.IsNullOrWhiteSpace(x)))
                {
                    string userNameWithoutDomain = userName.Split('/')[1].ToLowerInvariant();
                    userNameList.Add(userNameWithoutDomain);
                }
                //process list returned from cache provider method
                List<UserProfile> providedProfiles = cacheProvider.ProvideApproverProfileList(userNameList, token);
                //processes the list of user profiles for workflow approvers
                approverProfileList = _userService.ProcessFacilityUserProfilesForWorkflowApprovers(providedProfiles);
                return approverProfileList;
            }
            catch (Exception ex)
            {
                Logger.Error("Error in GetApproverProfileListForFacilityWorkflow", ex);
                return new List<UserProfile>();
            }
        }

        private List<FacilityWorkflowStep> FilterInvalidFacilityWorkflowSteps(SpanOfControlHierarchy hierarchy, string COID, IEnumerable<FacilityWorkflowStep> updatedOriginalSteps)
        {
            var filteredSteps = new List<FacilityWorkflowStep>();
            foreach (var step in updatedOriginalSteps)
            {
                var approver = step.Approver;
                var user = approver?.User;
                if (user == null)
                    continue;

                var profile = user.UserProfile;
                if (profile == null || profile.IsDeactivated)
                    continue;

                var roles = profile.Roles;
                if (roles == null || !roles.Any(r => r.RoleName != null && r.RoleName.ToLowerInvariant().Contains("approver")))
                    continue;

                if (!UserHasSpanOfControl(hierarchy, profile.SpanOfControl, COID))
                    continue;

                // If this step is a delegate, only add if the delegated approver exists in filteredSteps and has not been removed from the workflow list
                if (step.DelegatedByUserId != null)
                {
                    bool delegatedApproverExists = filteredSteps.Any(x => x.Approver.Delegate == step.DelegatedByUserId);
                    if (!delegatedApproverExists)
                        continue;
                }
                filteredSteps.Add(step);
            }
            return filteredSteps;
        }

        private static IEnumerable<FacilityWorkflowStep> UpdateOriginalFacilityWorkflowSteps(IEnumerable<FacilityWorkflowStep> originalSteps, List<UserProfile> userProfileList)
        {
            List<FacilityWorkflowStep> result = new List<FacilityWorkflowStep>();
            foreach (var updatedUserStep in originalSteps)
            {
                Approver approver = updatedUserStep.Approver;
                if (approver?.User?.UserProfile == null)
                {
                    UserProfile user = userProfileList.FirstOrDefault(y =>
                        y.UserName.Equals(approver.User.AccountName, StringComparison.OrdinalIgnoreCase));
                    if (user != null)
                    {
                        approver.User.UserProfile = user;
                    }
                }
                result.Add(updatedUserStep);
            }
            return result;
        }

        private bool HasSpanOfControl(SpanOfControlHierarchy hierarchy, List<SpanOfControl> spanOfControls, string coid)
        {
            return spanOfControls != null && spanOfControls.Any(y => (y.Hierarchy == OrganizationalLevelHierarchyType.Organization && Convert.ToInt32(y.ObjectId) == hierarchy.OrganizationID)
                                          || (y.Hierarchy == OrganizationalLevelHierarchyType.Company && Convert.ToInt32(y.ObjectId) == hierarchy.CompanyID)
                                          || (y.Hierarchy == OrganizationalLevelHierarchyType.Group && Convert.ToInt32(y.ObjectId) == hierarchy.GroupID)
                                          || (y.Hierarchy == OrganizationalLevelHierarchyType.Division && Convert.ToInt32(y.ObjectId) == hierarchy.DivisionID)
                                          || (y.Hierarchy == OrganizationalLevelHierarchyType.Market && Convert.ToInt32(y.ObjectId) == hierarchy.MarketID)
                                          || (y.Hierarchy == OrganizationalLevelHierarchyType.Facility && Convert.ToInt32(y.ObjectId) == Convert.ToInt32(coid)));
        }

        public bool UserHasSpanOfControl(SpanOfControlHierarchy hierarchy, List<SpanOfControl> spanOfControls, string coid)
        {
            return HasSpanOfControl(hierarchy, spanOfControls, coid);
        }
    }
}
