﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    /// <summary>
    /// Data transfer object representing the details of a Bill Only Review item.
    /// </summary>
    public class BillOnlyReviewItemDetailsDTO
    {
        /// <summary>
        /// Gets or sets the item identifier.
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// Gets or sets the item description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the main item identifier.
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the discount applied to the item.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the status type identifier.
        /// </summary>
        public int StatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the quantity to order.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the vendor name.
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// Gets or sets the vendor number.
        /// </summary>
        public string VendorNumber { get; set; }

        /// <summary>
        /// Gets or sets the reorder number.
        /// </summary>
        public string ReorderNumber { get; set; }

        /// <summary>
        /// Gets or sets the catalog number.
        /// </summary>
        public string CatalogNumber { get; set; }

        /// <summary>
        /// Gets or sets the general ledger (GL) code.
        /// </summary>
        public string GL { get; set; }

        /// <summary>
        /// Gets or sets the total cost of the item.
        /// </summary>
        public decimal? TotalCost { get; set; }

        /// <summary>
        /// Gets or sets the unit of measure code.
        /// </summary>
        public string UOMCode { get; set; }

        /// <summary>
        /// Gets or sets the par location.
        /// </summary>
        public string ParLocation { get; set; }

        /// <summary>
        /// Gets or sets the par value.
        /// </summary>
        public string Par { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the item is in stock.
        /// </summary>
        public bool? Stock { get; set; }

        /// <summary>
        /// Gets or sets the associated par item.
        /// </summary>
        public ParItem ParItem { get; set; }

        /// <summary>
        /// Gets or sets the associated item details.
        /// </summary>
        public Item Item { get; set; }

        /// <summary>
        /// Gets or sets the SPR (Special Purchase Request) detail.
        /// </summary>
        public SPRDetailDTO SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the VBO (Vendor Bill Only) hold item conversion details.
        /// </summary>
        public VboHoldItemConversionDto VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the collection of lot numbers.
        /// </summary>
        public IEnumerable<string> Lot { get; set; }

        /// <summary>
        /// Gets or sets the collection of serial numbers.
        /// </summary>
        public IEnumerable<string> Serial { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewItemDetailsDTO"/> class
        /// using the specified <see cref="BillOnlyReviewItemDAO"/> object.
        /// </summary>
        /// <param name="billOnlyReviewItemDAO">The data access object to map from.</param>
        public BillOnlyReviewItemDetailsDTO(BillOnlyReviewItemDAO billOnlyReviewItemDAO)
        {
            this.ItemId = billOnlyReviewItemDAO.ItemId;
            this.Description = billOnlyReviewItemDAO.Description;
            this.MainItemId = billOnlyReviewItemDAO.MainItemId;
            this.UnitCost = billOnlyReviewItemDAO.UnitCost;
            this.Discount = billOnlyReviewItemDAO.Discount;
            this.StatusTypeId = billOnlyReviewItemDAO.StatusTypeId;
            this.QuantityToOrder = billOnlyReviewItemDAO.QuantityToOrder;
            this.VendorName = billOnlyReviewItemDAO.VendorName;
            this.VendorNumber = billOnlyReviewItemDAO.VendorNumber;
            this.GL = billOnlyReviewItemDAO.GL;
            this.TotalCost = billOnlyReviewItemDAO.TotalCost;
            this.UOMCode = billOnlyReviewItemDAO.UOMCode;
            this.ParLocation = billOnlyReviewItemDAO.ParLocation;
            this.Par = billOnlyReviewItemDAO.Par;
            this.Stock = billOnlyReviewItemDAO.Stock;
            this.ReorderNumber = billOnlyReviewItemDAO.ReOrderNumber;
            this.CatalogNumber = billOnlyReviewItemDAO.CatalogNumber;
            this.Lot = billOnlyReviewItemDAO.Lot;
            this.Serial = billOnlyReviewItemDAO.Serial;
            this.ParItem = billOnlyReviewItemDAO.ParItem;
            this.Item = billOnlyReviewItemDAO.Item;
            this.SPRDetail = billOnlyReviewItemDAO.SPRDetail;
            this.VboHoldItemConversion = billOnlyReviewItemDAO.VboHoldItemConversion;
        }
    }
}