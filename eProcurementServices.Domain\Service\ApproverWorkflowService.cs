﻿using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.Interfaces.Services;
using eProcurementServices.Domain.Model.Profile;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace eProcurementServices.Domain.Service
{
    public class ApproverWorkflowService : IApproverWorkflowService
    {
        private readonly IUserService _userService;
        private readonly IFacilityWorkflowService _facilityWorkflowService;
        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public ApproverWorkflowService(IUserService userService, IFacilityWorkflowService facilityWorkflowService)
        {
            _userService = userService;
            _facilityWorkflowService = facilityWorkflowService;
        }
        public WorkflowValidationDTO ValidateAndSaveUserWorkflowSteps(SpanOfControlHierarchy hierarchy, string userName, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal)
        {
            try
            {
                IEnumerable<UserWorkflowStep> originalSteps = _userService.GetUserWorkflowSteps(userName, COID, (int)workflowType);

                //builds the list of user profiles from cache
                List<UserProfile> userProfileList = _userService.GetApproverProfileListForWorkflow(originalSteps);

                //updates the original workflow steps with the user profiles
                IEnumerable<UserWorkflowStep> updatedOriginalSteps = UpdateOriginalUserWorkflowSteps(originalSteps, userProfileList);

                //filters through the list of updated original workflow steps
                List<UserWorkflowStep> filteredSteps = FilterInvalidWorkflowSteps(hierarchy, COID, updatedOriginalSteps);

                WorkflowValidationDTO validations = _userService.ValidateUserWorkflow(userName, workflowType, COID, requisitionTotal);
                //checks to see if the original steps are equal to the filtered steps, if not equal then it will save the filtered steps as workflow
                var areEqual = updatedOriginalSteps.OrderBy(s => s.Id).Select(s => new { s.Id }).SequenceEqual(filteredSteps.Select(s => new { s.Id }));
                if (!areEqual)
                {
                    var saveWorkflowsDTO = new SaveWorkflowsDTO
                    {
                        workflows = new List<UserWorkflowDTO>
                        {
                            new UserWorkflowDTO
                            {
                                UserWorkflowSteps = filteredSteps,
                                UserName = userName,
                                WorkflowType = workflowType.ToString(),
                                WorkflowTypeId = (int)workflowType,
                                COID = COID
                            }
                        }
                    };
                    _userService.SaveWorkflows(userName, saveWorkflowsDTO);
                }
                //returns validations and warnings if any exist
                return validations;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error in {MethodBase.GetCurrentMethod().Name} for user '{userName}', workflowType '{workflowType}', COID '{COID}'.", ex);
                throw;
            }
        }

        private List<UserWorkflowStep> FilterInvalidWorkflowSteps(SpanOfControlHierarchy hierarchy, string COID, IEnumerable<UserWorkflowStep> updatedOriginalSteps)
        {
            var filteredSteps = new List<UserWorkflowStep>();
            foreach (var step in updatedOriginalSteps)
            {
                var approver = step.Approver;
                var user = approver?.User;
                if (user == null)
                    continue;

                var profile = user.UserProfile;
                if (profile == null || profile.IsDeactivated)
                    continue;

                var roles = profile.Roles;
                if (roles == null || !roles.Any(r => !string.IsNullOrEmpty(r.RoleName) && r.RoleName.IndexOf("approver", StringComparison.OrdinalIgnoreCase) >= 0))
                    continue;

                if (!_facilityWorkflowService.UserHasSpanOfControl(hierarchy, profile.SpanOfControl, COID))
                    continue;

                // If this step is a delegate, only add if the delegated approver exists in filteredSteps
                if (step.DelegatedByUserId != null)
                {
                    bool delegatedApproverExists = filteredSteps.Any(x => x.Approver.Delegate == step.DelegatedByUserId);
                    if (!delegatedApproverExists)
                        continue;
                }
                filteredSteps.Add(step);
            }
            return filteredSteps;
        }

        private static IEnumerable<UserWorkflowStep> UpdateOriginalUserWorkflowSteps(IEnumerable<UserWorkflowStep> originalSteps, List<UserProfile> userProfileList)
        {
            List<UserWorkflowStep> result = new List<UserWorkflowStep>();
            foreach (var updatedUserStep in originalSteps)
            {
                Approver approver = updatedUserStep.Approver;
                if (approver?.User?.UserProfile == null)
                {
                    UserProfile user = userProfileList.FirstOrDefault(y =>
                        y.UserName.Equals(approver.User.AccountName, StringComparison.OrdinalIgnoreCase));
                    if (user != null)
                    {
                        approver.User.UserProfile = user;
                    }
                }
                result.Add(updatedUserStep);
            }
            return result;
        }
    }
}
