﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{22345858-8AC1-40C6-935D-8C40CC5A167E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>eProcurementServices.Domain</RootNamespace>
    <AssemblyName>eProcurementServices.Domain</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <TargetFrameworkProfile />
    <RestorePackages>true</RestorePackages>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <LangVersion>7</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Production\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\QA\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <OutputPath>bin\Sandbox\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <OutputPath>bin\QASandbox\</OutputPath>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Castle.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.4.1\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="Evolve, Version=2.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Evolve.2.4.0\lib\net461\Evolve.dll</HintPath>
    </Reference>
    <Reference Include="JWT, Version=1.3.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\JWT.1.3.4\lib\3.5\JWT.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.WebApi.Versioning, Version=4.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Versioning.4.0.0\lib\net45\Microsoft.AspNet.WebApi.Versioning.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.3.1.8\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Http, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Http.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=6.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=9.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.9.2.0\lib\net45\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="MigraDoc.DocumentObjectModel, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc.1.50.5147\lib\net20\MigraDoc.DocumentObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="MigraDoc.Rendering, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc.1.50.5147\lib\net20\MigraDoc.Rendering.dll</HintPath>
    </Reference>
    <Reference Include="MigraDoc.RtfRendering, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc.1.50.5147\lib\net20\MigraDoc.RtfRendering.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc.1.50.5147\lib\net20\PdfSharp.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp.Charting, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc.1.50.5147\lib\net20\PdfSharp.Charting.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\packages\Pipelines.Sockets.Unofficial.2.2.8\lib\net472\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="Polly, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Polly.5.6.1\lib\net45\Polly.dll</HintPath>
    </Reference>
    <Reference Include="Smart.Core.Common, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Smart.Core.Common.3.4.1\lib\netstandard2.0\Smart.Core.Common.dll</HintPath>
    </Reference>
    <Reference Include="Smart.Core.Contracts, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Smart.Core.Contracts.1.2.1\lib\netstandard2.0\Smart.Core.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=*******, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.8.0\lib\net472\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.7.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.1\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=5.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.5.0.0\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Constants\AppInsightsRequestFilterKeywords.cs" />
    <Compile Include="Constants\CacheObjects.cs" />
    <Compile Include="Constants\DistributionPointList.cs" />
    <Compile Include="Constants\ComplianceCodeList.cs" />
    <Compile Include="Constants\IconNames.cs" />
    <Compile Include="Constants\ItemDetailsCodeList.cs" />
    <Compile Include="Constants\ToolTipMessages.cs" />
    <Compile Include="Constants\UOMList.cs" />
    <Compile Include="Constants\UpdateStatusWarnings.cs" />
    <Compile Include="Constants\UserProfileRoleNames.cs" />
    <Compile Include="Constants\WorkflowValidationWarnings.cs" />
    <Compile Include="Contracts\AddToCartResponse.cs" />
    <Compile Include="Contracts\CartAddItemRequest.cs" />
    <Compile Include="Contracts\CartAttributesResponse.cs" />
    <Compile Include="Contracts\CartDeleteItemRequest.cs" />
    <Compile Include="Contracts\CartItemExistsRequest.cs" />
    <Compile Include="Contracts\CartRequest.cs" />
    <Compile Include="Contracts\CartSubmitRequest.cs" />
    <Compile Include="Contracts\CartSubmitResponse.cs" />
    <Compile Include="Contracts\CartUpdateItemsRequest.cs" />
    <Compile Include="DTO\AddCommentDTO.cs" />
    <Compile Include="DTO\AdhocReviewDTO.cs" />
    <Compile Include="DTO\ApprovalListMultiResultsDTO.cs" />
    <Compile Include="DTO\ApproverDigitalSignOffDetailsDTO.cs" />
    <Compile Include="DTO\BillOnlyReviewDTO.cs" />
    <Compile Include="DTO\BillOnlyReviewItemDTO.cs" />
    <Compile Include="DTO\BillOnlyReviewDAO.cs" />
    <Compile Include="DTO\BillOnlyReview\BillOnlyReviewItemDAO.cs" />
    <Compile Include="DTO\BillOnlyReview\PrintBillOnlyReviewDetailsDTO.cs" />
    <Compile Include="DTO\BulkApprovalRequest.cs" />
    <Compile Include="DTO\BulkApprovalWorkflowGroupBy.cs" />
    <Compile Include="DTO\BulkApproverDetails.cs" />
    <Compile Include="DTO\BulkApproverJobDetailsDTO.cs" />
    <Compile Include="DTO\BulkApproverJobStatusDTO.cs" />
    <Compile Include="DTO\BulkApproverJobTracker.cs" />
    <Compile Include="DTO\BuyerModel.cs" />
    <Compile Include="DTO\PaginatedBORResult.cs" />
    <Compile Include="DTO\FacilityWorkflowDTO.cs" />
    <Compile Include="DTO\GetBulkJobDetailsDTO.cs" />
    <Compile Include="DTO\BulkApprover.cs" />
    <Compile Include="DTO\ContractDetailsRequestDTO.cs" />
    <Compile Include="DTO\DelegateAssignmentDTO.cs" />
    <Compile Include="DTO\FileAttachmentDTO.cs" />
    <Compile Include="DTO\FileNamesDTO.cs" />
    <Compile Include="DTO\ItemDetailsDTO.cs" />
    <Compile Include="DTO\ItemParDTO.cs" />
    <Compile Include="DTO\LegacyRequisitionReportDTO.cs" />
    <Compile Include="DTO\LegacyRequisitionReportRequestDTO.cs" />
    <Compile Include="DTO\LocatorDTO.cs" />
    <Compile Include="DTO\LocatorErrorsDTO.cs" />
    <Compile Include="DTO\LocatorLineDTO.cs" />
    <Compile Include="DTO\NamesDTO.cs" />
    <Compile Include="DTO\PersonalizationDTO.cs" />
    <Compile Include="DTO\ApproverDelegateInfoDTO.cs" />
    <Compile Include="DTO\ApprovalListResultsDTO.cs" />
    <Compile Include="DTO\ApprovalDTO.cs" />
    <Compile Include="DTO\LocationHydratedRequisitionDTO.cs" />
    <Compile Include="DTO\PurchasingDTOs\PurchasingRequisitionDTO.cs" />
    <Compile Include="DTO\PurchasingDTOs\PurchasingRequisitionItemsDTO.cs" />
    <Compile Include="DTO\PurchasingDTOs\PurchasingRequisitionQueDTO.cs" />
    <Compile Include="DTO\ReqTypeModel.cs" />
    <Compile Include="DTO\RequisitionForExportDTO.cs" />
    <Compile Include="DTO\RequisitionForVendorUserRequestDto.cs" />
    <Compile Include="DTO\RequisitionListMultiRequestDto.cs" />
    <Compile Include="DTO\PurchasingRequisitionReportResultsDTO.cs" />
    <Compile Include="DTO\RequisitionPurchasingAdvancedFilterRequest.cs" />
    <Compile Include="DTO\RequisitionPurchasingAdvancedFiltersDto.cs" />
    <Compile Include="DTO\PurchasingRequisitionReportParameters.cs" />
    <Compile Include="DTO\RequisitionReportExportResultsDTO.cs" />
    <Compile Include="DTO\RequisitionListRequestDto.cs" />
    <Compile Include="DTO\RequisitionListMultiResultsDTO.cs" />
    <Compile Include="DTO\RequisitionListResultsDTO.cs" />
    <Compile Include="DTO\RequisitionReportRequestDto.cs" />
    <Compile Include="DTO\SaveFacilityWorkflowDTO.cs" />
    <Compile Include="DTO\SaveUserEditInfoDTO.cs" />
    <Compile Include="DTO\SaveWorkflowsDTO.cs" />
    <Compile Include="DTO\SystemNotifcationTabInfoDTO.cs" />
    <Compile Include="DTO\SystemNotificationAdminDTO.cs" />
    <Compile Include="DTO\UserEditDTO.cs" />
    <Compile Include="DTO\InitiatePunchoutDTO.cs" />
    <Compile Include="DTO\PendingAdhocReviewDTO.cs" />
    <Compile Include="DTO\PODashboardDTO.cs" />
    <Compile Include="DTO\PunchOutRequestDTO.cs" />
    <Compile Include="DTO\RequisitionApproverDTO.cs" />
    <Compile Include="DTO\RequisitionWorkflowDTO.cs" />
    <Compile Include="DTO\RequisitionWorkflowStepDTO.cs" />
    <Compile Include="DTO\SpanOfControlDTO.cs" />
    <Compile Include="DTO\StatusUpdateDTO.cs" />
    <Compile Include="DTO\UserAccessReportDTO.cs" />
    <Compile Include="DTO\UserAccessReportProfileDTO.cs" />
    <Compile Include="DTO\UserReportInfoDTO.cs" />
    <Compile Include="DTO\UserReportInfoRequestDTO.cs" />
    <Compile Include="DTO\UserReportProfileDTO.cs" />
    <Compile Include="DTO\UserSetupWorkflowsDTO.cs" />
    <Compile Include="DTO\UserWorkflowDTO.cs" />
    <Compile Include="DTO\UserRequestDTO.cs" />
    <Compile Include="DTO\ApproverUpdateDTO.cs" />
    <Compile Include="DTO\DashboardDTO.cs" />
    <Compile Include="DTO\DashboardRequestDTO.cs" />
    <Compile Include="DTO\ErrorDTO.cs" />
    <Compile Include="DTO\FacilitiyNotificationsDTO.cs" />
    <Compile Include="DTO\Location.cs" />
    <Compile Include="DTO\ReportDTO.cs" />
    <Compile Include="DTO\RequisitionItemWithDetailsDTO.cs" />
    <Compile Include="DTO\RequisitionWithDetailsDTO.cs" />
    <Compile Include="DTO\RequisitionDTO.cs" />
    <Compile Include="DTO\RequisitionItemDTO.cs" />
    <Compile Include="DTO\SPRDetailDTO.cs" />
    <Compile Include="DTO\ValidateUserWorkflowsRequestDTO.cs" />
    <Compile Include="DTO\ValidationItem.cs" />
    <Compile Include="DTO\ValidationOfUserWorkflowsDTO.cs" />
    <Compile Include="DTO\VboHoldItemConversionDto.cs" />
    <Compile Include="DTO\VendorModel.cs" />
    <Compile Include="DTO\VendorResult.cs" />
    <Compile Include="DTO\WorkflowExportDTO.cs" />
    <Compile Include="DTO\WorkflowExportInputDTO.cs" />
    <Compile Include="DTO\WorkflowRequestValidationDTO.cs" />
    <Compile Include="DTO\WorkflowValidationDTO.cs" />
    <Compile Include="Enums\AddToCartStatus.cs" />
    <Compile Include="Enums\BulkApprovalJobStatus.cs" />
    <Compile Include="Enums\ChangeStatus.cs" />
    <Compile Include="Enums\ContractCodeType.cs" />
    <Compile Include="Enums\DeliveryMethodTypeEnum.cs" />
    <Compile Include="Enums\OrganizationalLevelHierarchyType.cs" />
    <Compile Include="Enums\CartType.cs" />
    <Compile Include="Enums\PassRole.cs" />
    <Compile Include="Enums\ReportType.cs" />
    <Compile Include="Enums\AddressType.cs" />
    <Compile Include="Enums\AppPartType.cs" />
    <Compile Include="Enums\MenuItemType.cs" />
    <Compile Include="Enums\ParType.cs" />
    <Compile Include="Enums\RequisitionAccessType.cs" />
    <Compile Include="Enums\RequisitionCreateTypeEnum.cs" />
    <Compile Include="Enums\RequisitionItemStatusType.cs" />
    <Compile Include="Enums\RequisitionSortOrder.cs" />
    <Compile Include="Enums\RequisitionType.cs" />
    <Compile Include="Enums\RequisitionStatusType.cs" />
    <Compile Include="Enums\RequisitionWorkflowStepType.cs" />
    <Compile Include="Enums\SmartCountryCodeEnum.cs" />
    <Compile Include="Enums\SPRTypeEnum.cs" />
    <Compile Include="Enums\UnitOfMeasureType.cs" />
    <Compile Include="Interfaces\Services\IApproverWorkflowService.cs" />
    <Compile Include="Model\Item\ItemPriceDetails.cs" />
    <Compile Include="Model\Reports\MigraDoc\BillOnlyReviewItemsDisplay.cs" />
    <Compile Include="Model\Reports\MigraDoc\BillOnlyReviewItemsDisplayMapper.cs" />
    <Compile Include="Model\UserAlertMessages\UserAlertMessageType.cs" />
    <Compile Include="Enums\WorkflowTypeEnum.cs" />
    <Compile Include="Interfaces\Reports\IReportOptions.cs" />
    <Compile Include="Interfaces\Services\IAdhocReviewService.cs" />
    <Compile Include="Interfaces\Services\IApproverService.cs" />
    <Compile Include="Interfaces\Services\IBillOnlyReviewService.cs" />
    <Compile Include="Interfaces\Services\IBulkApprovalService.cs" />
    <Compile Include="Interfaces\Services\ICartService.cs" />
    <Compile Include="Interfaces\Services\IClinicalDataService.cs" />
    <Compile Include="Interfaces\Services\ICOIDService.cs" />
    <Compile Include="Interfaces\Services\ICommentService.cs" />
    <Compile Include="Interfaces\Services\IConfigurationService.cs" />
    <Compile Include="Interfaces\Services\IContractService.cs" />
    <Compile Include="Interfaces\Services\IDigitalSignOffService.cs" />
    <Compile Include="Interfaces\Services\IFacilityWorkflowService.cs" />
    <Compile Include="Interfaces\Services\IFeatureFlagService.cs" />
    <Compile Include="Interfaces\Services\IIINItemService.cs" />
    <Compile Include="Interfaces\Services\ILocationService.cs" />
    <Compile Include="Interfaces\Services\IMenuService.cs" />
    <Compile Include="Interfaces\Services\IItemService.cs" />
    <Compile Include="Interfaces\Services\IPOService.cs" />
    <Compile Include="Interfaces\Services\IPunchOutService.cs" />
    <Compile Include="Interfaces\Services\IPurchasingService.cs" />
    <Compile Include="Interfaces\Services\IReportService.cs" />
    <Compile Include="Interfaces\Services\IRequisitionService.cs" />
    <Compile Include="Interfaces\Services\IProfileService.cs" />
    <Compile Include="Interfaces\Services\ISystemNotificationService.cs" />
    <Compile Include="Interfaces\Services\IUserAlertMessageService.cs" />
    <Compile Include="Interfaces\Services\IUserService.cs" />
    <Compile Include="Interfaces\Services\IVendorService.cs" />
    <Compile Include="Interfaces\Services\IViraService.cs" />
    <Compile Include="Interfaces\Utilities\ICacheHandler.cs" />
    <Compile Include="Interfaces\Utilities\ICacheManager.cs" />
    <Compile Include="Interfaces\Utilities\ICacheProvider.cs" />
    <Compile Include="Model\BillOnlyReview\BillOnlyReview.cs" />
    <Compile Include="Model\BillOnlyReview\BillOnlyReviewItem.cs" />
    <Compile Include="Model\BillOnlyReview\BillOnlyReviewPrintRequest.cs" />
    <Compile Include="Model\BillOnlyReview\BillOnlyReviewRequest.cs" />
    <Compile Include="Model\BillOnlyReview\BillOnlyReviewRequisition.cs" />
    <Compile Include="Model\BillOnlyReview\BillOnlyReviewSearchResult.cs" />
    <Compile Include="Model\Cart\Cart.cs" />
    <Compile Include="Model\Cart\CartItem.cs" />
    <Compile Include="Model\Cart\Item.cs" />
    <Compile Include="Model\Cart\ItemDetail.cs" />
    <Compile Include="Model\Cart\Item_RFID.cs" />
    <Compile Include="Model\Clinical\Patient.cs" />
    <Compile Include="Model\Clinical\Provider.cs" />
    <Compile Include="Model\Common\UnitOfMeasure.cs" />
    <Compile Include="Model\Contract\Contract.cs" />
    <Compile Include="Model\Contract\ContractReportResults.cs" />
    <Compile Include="Model\Contract\ContractDetails.cs" />
    <Compile Include="Model\DigitalSignOff\DigitalSignOffResponse.cs" />
    <Compile Include="Model\DigitalSignOff\DigitalSignOffUser.cs" />
    <Compile Include="Model\DigitalSignOff\ActiveDirectoryValidation.cs" />
    <Compile Include="Model\FacilityWorkflow\FacilityWorkflowStep.cs" />
    <Compile Include="Model\ItemInfo\ItemInfoModel.cs" />
    <Compile Include="Model\Item\AlternateUOM.cs" />
    <Compile Include="Model\Item\FacetFieldDTO.cs" />
    <Compile Include="Model\Item\FacetFieldResultDTO.cs" />
    <Compile Include="Model\Item\FacetFilter.cs" />
    <Compile Include="Model\Item\FStoreDeptModel.cs" />
    <Compile Include="Model\Item\GetParItemsWithLastOrderedInfoDTO.cs" />
    <Compile Include="Model\Item\IINItemRecordDTO.cs" />
    <Compile Include="Model\Item\Item.cs" />
    <Compile Include="Model\Item\ItemExportResults.cs" />
    <Compile Include="Model\Item\ItemInventory.cs" />
    <Compile Include="Model\Item\VboItemSearchCriteria.cs" />
    <Compile Include="Model\Item\ItemSearchCriteria.cs" />
    <Compile Include="Model\Item\ItemSearchDTO.cs" />
    <Compile Include="Model\Item\ItemSearchExport.cs" />
    <Compile Include="Model\Item\ItemSearchResult.cs" />
    <Compile Include="Model\Item\ItemSearchWithLastOrderedResult.cs" />
    <Compile Include="Model\Item\LastOrderDetailsDTO.cs" />
    <Compile Include="Model\Item\LotSerialPair.cs" />
    <Compile Include="Model\Item\ParItem.cs" />
    <Compile Include="Model\Item\ParItemWithLastOrdered.cs" />
    <Compile Include="Model\Item\TypeaheadItems.cs" />
    <Compile Include="Model\Item\TypeaheadItem.cs" />
    <Compile Include="Model\Item\UnitOfMeasureModel.cs" />
    <Compile Include="Model\Item\UOM.cs" />
    <Compile Include="Model\Locations\FacilityNotification.cs" />
    <Compile Include="Model\Locations\FacilityNotificationType.cs" />
    <Compile Include="Model\Menu\MenuItem.cs" />
    <Compile Include="Model\Menu\MenuItemPoint.cs" />
    <Compile Include="Model\Menu\MyApprovalMenuItem.cs" />
    <Compile Include="Model\Menu\MyRequisitionMenuItem.cs" />
    <Compile Include="Model\PO\POConfirmationDetail.cs" />
    <Compile Include="Model\PO\POHistory.cs" />
    <Compile Include="Model\PO\POInvoice.cs" />
    <Compile Include="Model\PO\POLineItem.cs" />
    <Compile Include="Model\PO\POHeader.cs" />
    <Compile Include="Model\PO\PODetails.cs" />
    <Compile Include="Model\PO\POLists.cs" />
    <Compile Include="Model\PO\POOptions.cs" />
    <Compile Include="Model\PO\POVendor.cs" />
    <Compile Include="Model\Profile\AdhocReviewer.cs" />
    <Compile Include="Model\Profile\AppPart.cs" />
    <Compile Include="Model\Profile\Approver.cs" />
    <Compile Include="Model\Profile\GLAccount.cs" />
    <Compile Include="Model\Profile\Profile.cs" />
    <Compile Include="Model\Profile\RequisitionVProBadgeLog.cs" />
    <Compile Include="Model\Profile\SpanOfControl.cs" />
    <Compile Include="Model\Profile\SpanOfControlHierarchy.cs" />
    <Compile Include="Model\Profile\VPROBadgeInRequest.cs" />
    <Compile Include="Model\Profile\Content.cs" />
    <Compile Include="Model\Profile\Status.cs" />
    <Compile Include="Model\Profile\VPROResponseFiltered.cs" />
    <Compile Include="Model\Profile\VPROReturnObject.cs" />
    <Compile Include="Model\Profile\User.cs" />
    <Compile Include="Model\Profile\UserSetupWorkflows.cs" />
    <Compile Include="Model\Profile\UserWorkflowStep.cs" />
    <Compile Include="Model\Profile\WorkflowType.cs" />
    <Compile Include="Model\Purchasing\Comment.cs" />
    <Compile Include="Model\Purchasing\ConfirmationLineItemDetails.cs" />
    <Compile Include="Model\Purchasing\GetConfirmationDetailsResponse.cs" />
    <Compile Include="Model\Purchasing\LineItemStatus.cs" />
    <Compile Include="Model\Purchasing\ConfirmationDetailsContent.cs" />
    <Compile Include="Model\Purchasing\ShippingStatus.cs" />
    <Compile Include="Model\Reports\PieChartReportOptions.cs" />
    <Compile Include="Model\Reports\ColumnChartReportOptions.cs" />
    <Compile Include="Model\Reports\BarChartReportOptions.cs" />
    <Compile Include="Model\Reports\ReportAnnotationOptions.cs" />
    <Compile Include="Model\Reports\ReportBarOptions.cs" />
    <Compile Include="Model\Reports\ReportColumn.cs" />
    <Compile Include="Model\Reports\ReportData.cs" />
    <Compile Include="Model\Reports\ReportHorizontalAxisOptions.cs" />
    <Compile Include="Model\Reports\ReportLegendOptions.cs" />
    <Compile Include="Model\Reports\ReportRow.cs" />
    <Compile Include="Model\Reports\ReportRowContainer.cs" />
    <Compile Include="Model\Reports\ReportTextStyleOptions.cs" />
    <Compile Include="Model\Reports\ReportTooltipOptions.cs" />
    <Compile Include="Model\Requisition\ClinicalUseDetail.cs" />
    <Compile Include="Model\Requisition\Comment.cs" />
    <Compile Include="Model\Requisition\CommentNotificationRequisition.cs" />
    <Compile Include="Model\Requisition\CommentNotificationRequisitionsDTO.cs" />
    <Compile Include="Model\Requisition\CommentsResponse.cs" />
    <Compile Include="Model\Requisition\DeliveryMethodType.cs" />
    <Compile Include="Model\Requisition\FileAttachment.cs" />
    <Compile Include="Model\Requisition\InFlightQty.cs" />
    <Compile Include="Model\Requisition\Requisition.cs" />
    <Compile Include="Model\Requisition\RequisitionPurchasingReportItemDTO.cs" />
    <Compile Include="Model\Requisition\RequisitionDigitalSignOff.cs" />
    <Compile Include="Model\Requisition\RequisitionItem.cs" />
    <Compile Include="Model\Requisition\RequisitionStatusDTO.cs" />
    <Compile Include="Model\Profile\Address.cs" />
    <Compile Include="Model\Profile\Department.cs" />
    <Compile Include="Model\Profile\Facility.cs" />
    <Compile Include="Model\Profile\Par.cs" />
    <Compile Include="Model\Profile\UserProfile.cs" />
    <Compile Include="Model\Profile\UserRole.cs" />
    <Compile Include="Model\Requisition\RequisitionStatusHistory.cs" />
    <Compile Include="Model\Requisition\SPRDetail.cs" />
    <Compile Include="Model\Requisition\SPRType.cs" />
    <Compile Include="Model\Requisition\VboHoldItemConversion.cs" />
    <Compile Include="Model\Search\AzureGlAccountSearch.cs" />
    <Compile Include="Model\Search\FacetField.cs" />
    <Compile Include="Model\Search\FacetFieldResult.cs" />
    <Compile Include="Model\Search\FilterCriteria.cs" />
    <Compile Include="Model\Search\AzureGlAccountSearchResult.cs" />
    <Compile Include="Model\Search\ItemResult.cs" />
    <Compile Include="Model\Search\ItemTypeaheadResult.cs" />
    <Compile Include="Model\Search\ItemVendorSearchResult.cs" />
    <Compile Include="Model\Search\GlAccountTypeaheadResult.cs" />
    <Compile Include="Model\Search\ItemVendorTypeaheadResult.cs" />
    <Compile Include="Model\Search\SearchCriteria.cs" />
    <Compile Include="Model\SystemNotification\SystemNotification.cs" />
    <Compile Include="Model\UserAlertMessages\UserAlertMessage.cs" />
    <Compile Include="Model\UserAlertMessages\UserAlertMessageRequest.cs" />
    <Compile Include="Model\Vendors\PunchOutVendor.cs" />
    <Compile Include="Model\Vendors\TypeAheadVendors.cs" />
    <Compile Include="Model\Vendors\OrderTimes.cs" />
    <Compile Include="Model\Vendors\Vendor.cs" />
    <Compile Include="Model\Vendors\VendorDetails.cs" />
    <Compile Include="Model\Vendors\VendorHeaderInfo.cs" />
    <Compile Include="Model\Vira\ViraItemStatus.cs" />
    <Compile Include="Model\Vira\ViraSPRItemRequest.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PunchOut\cXMLGenerated_007.cs" />
    <Compile Include="PunchOut\cXmlDtdResolver.cs" />
    <Compile Include="PunchOut\cXMLRequisitionMapper.cs" />
    <Compile Include="PunchOut\PunchOutBase.cs" />
    <Compile Include="PunchOut\PunchOutDELL.cs" />
    <Compile Include="PunchOut\PunchOutInsight.cs" />
    <Compile Include="PunchOut\PunchOutCDW.cs" />
    <Compile Include="PunchOut\PunchOutOperation.cs" />
    <Compile Include="PunchOut\PunchOutUtility.cs" />
    <Compile Include="PunchOut\PunchOutVendorFactory.cs" />
    <Compile Include="PunchOut\PunchOutXmlNavigator.cs" />
    <Compile Include="Model\Profile\ActiveApprover.cs" />
    <Compile Include="Model\Profile\ActiveApproverDto.cs" />
    <Compile Include="Service\AdhocReviewService.cs" />
    <Compile Include="Service\ApproverService.cs" />
    <Compile Include="Model\Profile\ApproverWorkflow.cs" />
    <Compile Include="Model\Profile\ApproverWorkflowDto.cs" />
    <Compile Include="Service\ApproverWorkflowService.cs" />
    <Compile Include="Service\BillOnlyReviewService.cs" />
    <Compile Include="Service\BulkApprovalService.cs" />
    <Compile Include="Service\CartService.cs" />
    <Compile Include="Service\ClinicalDataService.cs" />
    <Compile Include="Service\COIDService.cs" />
    <Compile Include="Service\CommentService.cs" />
    <Compile Include="Service\ConfigurationService.cs" />
    <Compile Include="Service\ContractService.cs" />
    <Compile Include="Interfaces\Services\IGlAccountService.cs" />
    <Compile Include="Service\DigitalSignOffService.cs" />
    <Compile Include="Service\FacilityWorkflowService.cs" />
    <Compile Include="Service\FeatureFlagService.cs" />
    <Compile Include="Service\IINItemService.cs" />
    <Compile Include="Service\GlAccountService.cs" />
    <Compile Include="Service\ItemService.cs" />
    <Compile Include="Service\LocationService.cs" />
    <Compile Include="Service\MenuService.cs" />
    <Compile Include="Service\POService.cs" />
    <Compile Include="Service\PunchOutService.cs" />
    <Compile Include="Service\PurchasingService.cs" />
    <Compile Include="Service\ReportService.cs" />
    <Compile Include="Service\RequisitionService.cs" />
    <Compile Include="Service\ProfileService.cs" />
    <Compile Include="Service\SystemNotificationService.cs" />
    <Compile Include="Service\UserAlertMessageService.cs" />
    <Compile Include="Service\UserPartService.cs" />
    <Compile Include="Service\UserService.cs" />
    <Compile Include="Service\VendorService.cs" />
    <Compile Include="Service\ViraService.cs" />
    <Compile Include="Utility\CacheHandler.cs" />
    <Compile Include="Utility\CacheProvider.cs" />
    <Compile Include="Utility\ExtensionMethods.cs" />
    <Compile Include="Utility\HTTPRunTimeCacheManager.cs" />
    <Compile Include="Utility\PassUtility.cs" />
    <Compile Include="Utility\RedisCacheManager.cs" />
    <Compile Include="Utility\TokenGeneratorService.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <EmbeddedResource Include="PunchOut\cXML1.2.014.dtd">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="PunchOut\cXML1.2.007.dtd">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="PunchOut\cXML1.2.009.dtd" />
    <None Include="PunchOut\cXML1.2.007.xsd">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eProcurementServices.Utility\eProcurementServices.Utility.csproj">
      <Project>{1b8582fd-1378-43dc-b8bd-be9559625757}</Project>
      <Name>eProcurementServices.Utility</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>