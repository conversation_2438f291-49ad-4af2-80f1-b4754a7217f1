﻿using eProcurementServices.Domain.DTO.BillOnlyReview;
using eProcurementServices.Domain.Model.DigitalSignOff;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Model.Requisition;
using System;
using System.Collections.Generic;

namespace eProcurementServices.Domain.DTO
{
    /// <summary>
    /// DTO for Bill Only Review Requisition with Details, including requisition, patient, provider, and item information.
    /// </summary>
    public class BillOnlyReviewDAO 
    {
        /// <summary>
        /// Gets or sets the unique identifier for the requisition.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the status type ID of the requisition.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition status type.
        /// </summary>
        public string RequisitionStatusTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the requisition type ID.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition type.
        /// </summary>
        public string RequisitionTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the company name associated with the requisition.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the facility name associated with the requisition.
        /// </summary>
        public string FacilityName { get; set; }

        /// <summary>
        /// Gets or sets the department name associated with the requisition.
        /// </summary>
        public string DepartmentName  { get; set; }

        /// <summary>
        /// Gets or sets the patient account number or identifier.
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// Gets or sets the patient name.
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// Gets or sets the provider name.
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// Gets or sets the location identifier for the requisition.
        /// </summary>
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the date and time the requisition was created.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the user who created the requisition.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is for a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the submission type ID for the requisition.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the first name of the requisitioner.
        /// </summary>
        public string RequisitionerFirstName { get; set; }

        /// <summary>
        /// Gets or sets the last name of the requisitioner.
        /// </summary>
        public string RequisitionerLastName { get; set; }

        /// <summary>
        /// Gets or sets the country code associated with the requisition.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets the procedure date, if applicable.
        /// </summary>
        public DateTime? ProcedureDate { get; set; }

        /// <summary>
        /// Gets or sets the collection of items associated with the requisition.
        /// </summary>
        public IEnumerable<BillOnlyReviewItemDAO> RequisitionItems { get; set; }

        /// <summary>
        /// Gets or sets the vendor badge log for the requisition.
        /// </summary>
        public RequisitionVProBadgeLog VProBadgeLog { get; set; }

        /// <summary>
        /// Gets or sets the digital sign off information for the requisition.
        /// </summary>
        public RequisitionDigitalSignOff DigitalSignOff { get; set; }

        /// <summary>
        /// Gets or sets the digital sign off user information for the requisition.
        /// </summary>
        public DigitalSignOffUser DigitalSignOffUser { get; set; }
    }
}
