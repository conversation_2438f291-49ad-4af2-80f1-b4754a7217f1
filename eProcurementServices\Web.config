﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler" />
		<section name="featureFlags" type="System.Configuration.NameValueSectionHandler" />
	</configSections>
	<connectionStrings />
	<appSettings>
		<add key="webpages:Version" value="*******" />
		<add key="webpages:Enabled" value="false" />
		<add key="ClientValidationEnabled" value="true" />
		<add key="UnobtrusiveJavaScriptEnabled" value="true" />
		<add key="log4net.Config" value="web.config" />
		<add key="HomeAPIUrl" value="http://dev-api.healthtrustpg.com/v1/api/" />
		<add key="SecurityAPINewUrl" value="https://sbx-api-smartclientauthorization.healthtrustpg.com/" />
		<add key="SecurityAPIUrl" value="https://dev-api.nsa.healthtrustpg.com/v1/" />
		<add key="SecurityAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD" />
		<add key="ProfileAPIUrl" value="https://dev-api-profile.healthtrustpg.com/v1/" />
		<add key="RequisitionAPIUrl" value="https://local-api-requisitions.healthtrustpg.com/" />
		<add key="CatalogAPIUrl" value="https://qasbx-api-catalogservices.healthtrustpg.com/" />
		<add key="PurchasingAPIUrl" value="https://sbx-api-purchasing.healthtrustpg.com/" />
		<add key="SwaggerRootUrl" value="https://local-api-eprocurement.healthtrustpg.com" />
		<add key="AcceptedCORSOrigins" value="http://local-eprocurement.healthtrustpg.com,https://local-eprocurement.healthtrustpg.com,http://local-smart.healthtrustpg.com,https://local-smart.healthtrustpg.com,http://localhost:4200" />
		<add key="jwTokenKey" value="T4eRe83aNaNzr1nTIl0g4tsFFzJefIg4823Fh82n" />
		<add key="SSO_grant_type" value="client_credentials" />
		<add key="SSO_client_id" value="dev-smart-sec" />
		<add key="SSO_client_secret" value="********************************" />
		<add key="SSO_scope" value="smart_security" />
		<add key="SSO_response_type" value="code" />
		<add key="SmartClinetAuthUrl" value="https://qa-sso.healthtrustpg.com/as/token.oauth2" />
		<add key="jwTokenExpireDays" value="7" />
		<add key="aspnet:MaxJsonDeserializerMembers" value="**********" />
		<add key="NumberOfDaysForPullingRequisitions" value="30" />
		<add key="EnvironmentDomainList" value="HCA,HCADEV,NONAFFILDEV,CapellaDEV,LPNTDEV,TRIADDEV" />
		<add key="AWSUserName" value="eproc-service-account" />
		<add key="AWSAccessKeyId" value="********************" />
		<add key="AWSSecretAccessKey" value="3AR/pJPRidUWRh0a3zdgVe41BuguqhPSDLQIazKV" />
		<add key="AWSBucket" value="Eproc-dev" />
		<add key="AzAccountName" value="hpgsmartdev26249658stlr" />
		<add key="AzAccountKey" value="****************************************************************************************" />
		<add key="AzureBlobEndPoint" value="DefaultEndpointsProtocol=https;AccountName=hpgsmartdev26249658stlr;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
		<add key="AzureBlobContainer" value="procurement" />
		<add key="AzureGlAccountSearchUrl" value="https://ht-ti-search-dev.search.windows.net/" />
		<add key="AzureGlAccountSearchApiKey" value="BA4C0AC3895BD8AF8C643C0312251D19" />
		<add key="punchOutTokenExpirationDays" value="2" />
		<add key="eProcurementRequisitionUrl" value="https://local-smart.healthtrustpg.com/Procurement/MyRequisition/{0}" />
		<add key="punchOutEncryptionTokenKey" value="A89Gjhf976KL;KGF-99045JLMFB09ML,K456098832hfdn233900vn" />
		<add key="punchOutBrowserFormPostURL" value="https://local-api-eprocurement.healthtrustpg.com/PunchOut/ReceivePunchOutOrderMessage?vendorName={0}" />
		<add key="CacheFacilityDeptExpiryTimer" value="3" />
		<add key="AppInsightsEnabled" value="False" />
		<add key="ikey" value="059f9b41-f9cd-4d50-a025-521516ae8226" />
		<add key="COIDList" value="00995,07847,34222,34242" />
		<add key="BulkApprovalServiceAuthKey" value="5EC289B2-AB68-4BB5-BD42-0FF837B9A057" />
		<add key="useDevelopmentDomain" value="true" />
		<add key="useQaDomain" value="false" />
		<add key="securityProtocol" value="https" />
		<add key="vendorUsersGroupOne" value="hcadev/pya7593" />
		<add key="vendorUsersGroupTwo" value="hcadev/LSI6344" />
		<add key="vendorUsersWithNoAffils" value="" />
		<add key="coidsForQATesting" value="57110,57111" />
		<add key="PingTokenValidateUrl" value="https://qa-sso.healthtrustpg.com/as/introspect.oauth2" />
		<add key="PingAccessTokenValidationAuthority" value="https://qa-sso.healthtrustpg.com" />
		<add key="PingAccessTokenValidationClientId:0" value="dev-SMART" />
		<add key="PingAccessTokenValidationClientId:1" value="dev-smart-mobile-dashboard" />
		<add key="CorpAdParentDomains" value="corpad.net,corpaddev.net,corpadqa.net" />
		<add key="CorpAdChildDomains" value="hca,lpnt,capella,hcadev,hcaqa,nonaffil,nonaffildev,nonaffilqa" />
		<add key="EntryExitServiceCentralAPIUrl" value="https://hcaservicecentraldev.service-now.com/" />
		<add key="EntryExitServiceCentralUser" value="svc_SmartInbound" />
		<add key="EntryExitServiceCentralPwd" value="CWJm6sjpnWwwazSwjtT_be" />
		<add key="ClientId" value="qa-SMART-procurement" />
		<add key="DigitalSignOffUserInfoLink" value="https://api-dev.internal.medcity.net/smart-user-search/SMART-User-Search-Api/v1/"/>
		<add key="DigitalSignOffUserApiKey" value="Bearer dev_i59HdDJ1VrGZCCp33WoK1cwBi2LcILtM"/>
		<add key="DigitalSignOffUserDomain" value="hcadev"/>
		<add key="RedisConnectionString" value="localhost:6379"/>
		<add key="RedisAuth" value="none"/>
		<add key="RedisKeyPrefix" value="LocalProcurement:"/>
		<add key="ViraRetryInterval" value="1800000"/>
		<add key="PilotCOIDsRemoveIClassPars" value="" />
		<add key="IClassParNotificationMessage" value="As of 02/20/2025, IClass PARs will no longer be available"/>
		<add key="CacheUserProfileExpiryTimer"  value="0.03"/>
		<!-- Feature Flags -->
		<!--<add key="FFIsEntryExitEnabled" value="true" />-->

		<!-- Max Degree of Parallelism -->
		<add key="GetUsersWithRoleAndSOCParallelism" value="10" />
	</appSettings>

	<system.web>
		<compilation debug="true" targetFramework="4.7.2" />
		<httpRuntime targetFramework="4.7.2" executionTimeout="180" maxRequestLength="32768" />
		<authorization>
			<allow users="*" />
		</authorization>
		<httpModules>
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" />
		</httpModules>
	</system.web>
	<system.web.extensions>
		<scripting>
			<webServices>
				<jsonSerialization maxJsonLength="50000000" />
			</webServices>
		</scripting>
	</system.web.extensions>
	<system.webServer>
		<security>
			<authentication>
				<anonymousAuthentication enabled="true"/>
				<basicAuthentication enabled="false" />
				<windowsAuthentication enabled="true" />
			</authentication>
		</security>
		<validation validateIntegratedModeConfiguration="false" />
		<modules>
			<remove name="ApplicationInsightsWebTracking" />
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" />
			<add name="CorrelationIdModule" type="eProcurementServices.Modules.CorrelationIdModule" />
			<add name="UserModule" type="eProcurementServices.Modules.UserModule" />
		</modules>

		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Security.Cryptography.Algorithms" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http.WebHost" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Practices.Unity" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.0" newVersion="3.5.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="*******-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.0.12.0" newVersion="2.0.12.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.4.0.0" newVersion="7.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-7.4.0.0" newVersion="7.4.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<system.net>
		<defaultProxy>
			<proxy usesystemdefault="True" bypassonlocal="True" proxyaddress="http://proxy.nas.medcity.net:80" />
			<bypasslist>
				<add address="[a-z]+\.healthtrustpg\.com" />
			</bypasslist>
		</defaultProxy>
		<mailSettings>
			<smtp from="<EMAIL>">
				<network host="smtp-gw.nas.medcity.net" />
			</smtp>
		</mailSettings>
		<settings>
			<httpWebRequest useUnsafeHeaderParsing="true" />
		</settings>
	</system.net>
	<log4net>
		<appender name="DebugFileAppender" type="log4net.Appender.RollingFileAppender">
			<param name="Threshold" value="DEBUG" />
			<param name="File" value="Logs/" />
			<param name="MaxSizeRollBackups" value="-1" />
			<param name="RollingStyle" value="Date" />
			<param name="StaticLogFileName" value="false" />
			<param name="DatePattern" value="yyyyMMdd'_Debug.log'" />
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} (%F:%L) %m%n" />
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter">
				<levelMin value="DEBUG" />
				<levelMax value="FATAL" />
			</filter>
		</appender>
		<appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
			<param name="Threshold" value="INFO" />
			<param name="File" value="Logs/" />
			<param name="MaxSizeRollBackups" value="-1" />
			<param name="RollingStyle" value="Date" />
			<param name="StaticLogFileName" value="false" />
			<param name="DatePattern" value="yyyyMMdd'_Info.log'" />
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n" />
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter">
				<levelMin value="INFO" />
				<levelMax value="WARN" />
			</filter>
		</appender>
		<appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
			<param name="Threshold" value="ERROR" />
			<param name="File" value="Logs/" />
			<param name="MaxSizeRollBackups" value="-1" />
			<param name="RollingStyle" value="Date" />
			<param name="StaticLogFileName" value="false" />
			<param name="DatePattern" value="yyyyMMdd'_Error.log'" />
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n" />
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter">
				<levelMin value="ERROR" />
				<levelMax value="FATAL" />
			</filter>
		</appender>
		<appender name="aiAppender" type="Microsoft.ApplicationInsights.Log4NetAppender.ApplicationInsightsAppender, Microsoft.ApplicationInsights.Log4NetAppender">
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n" />
			</layout>
		</appender>
		<root>
			<appender-ref ref="DebugFileAppender" />
			<appender-ref ref="InfoFileAppender" />
			<appender-ref ref="ErrorFileAppender" />
			<appender-ref ref="aiAppender" />
		</root>
	</log4net>
	<featureFlags>
		<add key="FFIsEntryExitEnabled" value="true" />
		<add key="FFIsEntryExitEnabledForStandardRequisition" value="true" />
		<add key="DigitalSignOffIsEnabled" value="true" />
		<add key="FeatureFlagTotalReqAmount" value="false"/>
		<add key="FFVPROBadgeInFE" value="true"/>
		<add key="FFVPROBadgeInBE" value="true"/>
		<add key="ViraAutomationOn" value="true"/>
		<add key="BORPrintFeatureOn" value="true" />
		<add key="AutoRemoveApproverFeatureOn" value="true" />
	</featureFlags>
</configuration>