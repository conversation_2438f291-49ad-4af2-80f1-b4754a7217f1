﻿using eProcurementServices.Domain.Model.Item;
using System.Collections.Generic;

namespace eProcurementServices.Domain.DTO.BillOnlyReview
{
    /// <summary>
    /// Data Access Object representing a Bill Only Review Item, including item, vendor, cost, and related requisition details.
    /// </summary>
    public class BillOnlyReviewItemDAO
    {
        private ParItem _parItem;
        private Item _item;
        /// <summary>
        /// Gets or sets the unique identifier for the item.
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// Gets or sets the description of the item.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the main item identifier, if applicable.
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the discount percentage applied to the item.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the status type identifier for the item.
        /// </summary>
        public int StatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the quantity to order for the item.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the vendor name.
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// Gets or sets the vendor number.
        /// </summary>
        public string VendorNumber { get; set; }

        /// <summary>
        /// Gets or sets the reorder number for the item.
        /// </summary>
        public string ReorderNumber { get; set; }

        /// <summary>
        /// Gets or sets the catalog number for the item.
        /// </summary>
        public string CatalogNumber { get; set; }

        /// <summary>
        /// Gets or sets the general ledger (GL) code for the item.
        /// </summary>
        public string GL { get; set; }

        /// <summary>
        /// Gets or sets the total cost for the item.
        /// </summary>
        public decimal? TotalCost { get; set; }

        /// <summary>
        /// Gets or sets the unit of measure code for the item.
        /// </summary>
        public string UOMCode { get; set; }

        /// <summary>
        /// Gets or sets the PAR location for the item.
        /// </summary>
        public string ParLocation { get; set; }

        /// <summary>
        /// Gets or sets the PAR identifier for the item.
        /// </summary>
        public string Par { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the item is stock.
        /// </summary>
        public bool? Stock { get; set; }

        /// <summary>
        /// Gets or sets the lot numbers associated with the item.
        /// </summary>
        public IEnumerable<string> Lot { get; set; }

        /// <summary>
        /// Gets or sets the serial numbers associated with the item.
        /// </summary>
        public IEnumerable<string> Serial { get; set; }

        /// <summary>
        /// Gets or sets the procedure code for the item.
        /// </summary>
        public string ProcedureCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the item is chargeable.
        /// </summary>
        public string Chargeable { get; set; }

        /// <summary>
        /// Gets or sets the PAR item details.
        /// </summary>
        public ParItem ParItem { get; set; }       

        /// <summary>
        /// Gets or sets the item details.
        /// </summary>
        public Item Item { get; set; }

        /// <summary>
        /// Gets or sets the SPR (Special Purchase Request) detail for the item.
        /// </summary>
        public SPRDetailDTO SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the VBO (Vendor Bill Only) hold item conversion details.
        /// </summary>
        public VboHoldItemConversionDto VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the PO number for the item.
        /// </summary>
        public int? PONumber { get; set; }

        /// <summary>
        /// Gets or sets the parent system ID for the item.
        /// </summary>
        public string ParentSystemId { get; set; }

        /// <summary>
        /// Gets or sets the quantity fulfilled for the item.
        /// </summary>
        public int? QuantityFulfilled { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this is a rush order.
        /// </summary>
        public bool IsRush { get; set; }

        /// <summary>
        /// Gets or sets the upcharge amount for the item.
        /// </summary>
        public decimal? UpchargeAmount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the item is on contract.
        /// </summary>
        public bool? OnContract { get; set; }

        /// <summary>
        /// Gets or sets the parts warranty in months.
        /// </summary>
        public byte? PartsWarrantyMonths { get; set; }

        /// <summary>
        /// Gets or sets the labor warranty in months.
        /// </summary>
        public byte? LaborWarrantyMonths { get; set; }
    }
}
