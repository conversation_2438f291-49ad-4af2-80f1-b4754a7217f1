﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using System.ComponentModel.DataAnnotations;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    /// <summary>
    /// Data transfer object representing a Bill Only Review requisition item.
    /// </summary>
    public class BillOnlyReviewItemDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the requisition item.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the requisition identifier to which this item belongs.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the item identifier.
        /// </summary>
        [StringLength(50)]
        public string ItemId { get; set; }

        /// <summary>
        /// Gets or sets the item description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the main item identifier.
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// Gets or sets the discount applied to the item.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the quantity to order.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the VBO hold item conversion details.
        /// </summary>
        public VboHoldItemConversionDto VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the SPR detail information.
        /// </summary>
        public SPRDetailDTO SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the status type ID of the requisition item.
        /// </summary>
        public int RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the associated item details originating from smart item service if available.
        /// </summary>
        public Item Item { get; set; }

        /// <summary>
        /// Gets or sets the associated PAR item details originating from par service if available.
        /// </summary>
        public ParItem ParItem { get; set; }
    }
}
