﻿using eProcurementServices.Domain.Constants;
using eProcurementServices.Domain.DTO;
using eProcurementServices.Domain.Enums;
using eProcurementServices.Domain.Interfaces.Services;
using eProcurementServices.Domain.Interfaces.Utilities;
using eProcurementServices.Domain.Model.Profile;
using eProcurementServices.Domain.Model.Vendors;
using eProcurementServices.Domain.Utility;
using eProcurementServices.Utility.Model;
using eProcurementServices.Utility.WebAPI;
using log4net;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Web;

namespace eProcurementServices.Domain.Service
{
    public class UserService : IUserService
    {
        private const string SaveUserInfoMethod = "User/SaveUserEditInfoAsync/";
        private const string GetProfileMethod = "Profile/";
        private const string GetUserWorkflowStepsMethod = "User/GetUserWorkflowSteps/";
        private const string GetUserWorkflowsForExportMethod = "User/GetUserWorkflowsForExport/";
        private const string GetWorkflowTypesMethod = "User/GetAllWorkflowTypes/";
        private const string GetUserWorkflowTypesMethod = "User/GetAllUserWorkflowTypes/";
        private const string SaveWorkflowMethod = "User/SaveUserWorkflowSteps/";
        private const string SaveUserWorkflowsMethod = "User/SaveWorkflows/";
        private const string ValidateUserWorkflowMethod = "User/ValidateUserWorkflow";
        private const string GetValidationOfUserWorkflowsMethod = "User/GetValidationOfUserWorkflows";
        private const string GetEProUserMethod = "User/GetUser";
        private const string GetEProUserWithoutDomainMethod = "User/GetUserWithoutDomain";
        private const string PostEProUpdateUserName = "User/UpdateUserName";
        private const string RetrieveUserReportInfoMethod = "User/RetrieveUserReportInfo/";
        private const string GetUserEditUsersMethod = "User/GetUserEditUsers/";
        private const string PostUserDetails = "users/details";
        private const string CreateVProBadgeLog = "VPro/CreateVProBadgeLog";
        private readonly string _appKey = ConfigurationManager.AppSettings.Get("SecurityAPIKey");
        private readonly string _newSecurityEndpoint = ConfigurationManager.AppSettings.Get("SecurityAPINewUrl");
        private readonly string _reqApiEndpoint = ConfigurationManager.AppSettings.Get("RequisitionAPIUrl");
        private readonly string _profileEndpoint = ConfigurationManager.AppSettings.Get("ProfileAPIUrl");
        private readonly bool _useDevelopmentDomain = bool.Parse(ConfigurationManager.AppSettings.Get("useDevelopmentDomain"));
        private readonly bool _useQaDomain = bool.Parse(ConfigurationManager.AppSettings.Get("useQaDomain"));

        private readonly List<SpanOfControl> _testCoidSpanOfControls = !string.IsNullOrWhiteSpace(ConfigurationManager.AppSettings.Get("coidsForQATesting")) ? ConfigurationManager.AppSettings.Get("coidsForQATesting").Split(',')
            .Select(x => new SpanOfControl { Hierarchy = OrganizationalLevelHierarchyType.Facility, ObjectId = x }).ToList()
                : new List<SpanOfControl>();

        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, int? workflowTypeId = null)
        {
            var parameters = new Dictionary<string, string>
                                {
                                    { "userName", userName },
                                    { "COID", COID }
                                };
            if (workflowTypeId != null)
            {
                parameters.Add("workflowTypeId", workflowTypeId.ToString());
            }

            var userWorkflowSteps = ApiUtility.ExecuteApiGetTo<List<UserWorkflowStep>>(_reqApiEndpoint, GetUserWorkflowStepsMethod, parameters);

            foreach (var userWorkflowStep in userWorkflowSteps)
            {
                if (userWorkflowStep.Approver != null && userWorkflowStep.Approver.User != null)
                {
                    userWorkflowStep.Approver.User.UserProfile = GetUserProfile(userWorkflowStep.Approver.User.AccountName);
                }
            }

            return userWorkflowSteps;
        }

        public IEnumerable<WorkflowExportDTO> GetUserWorkflowsForExport(WorkflowExportInputDTO workflowExportInputDTO)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<IEnumerable<WorkflowExportDTO>>(_reqApiEndpoint, GetUserWorkflowsForExportMethod, null, workflowExportInputDTO).ToList();
        }

        public IEnumerable<UserWorkflowDTO> SaveWorkflows(string saversUserName, SaveWorkflowsDTO saveWorkflowsDTO)
        {
            foreach (var userWorkflow in saveWorkflowsDTO.workflows)
            {
                userWorkflow.UserWorkflowSteps = RemoveUsersandApprovers(userWorkflow.UserWorkflowSteps, saversUserName);
            }

            return ApiUtility.ExecuteApiPostWithContentTo<IEnumerable<UserWorkflowDTO>>(_reqApiEndpoint, SaveUserWorkflowsMethod, new Dictionary<string, string> { { "updater", saversUserName } }, saveWorkflowsDTO);
        }

        public IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string userName, int workflowTypeId, string workflowUser, IEnumerable<UserWorkflowStep> userWorkflowSteps)
        {
            userWorkflowSteps = RemoveUsersandApprovers(userWorkflowSteps, userName);

            var userWorkflowDTO = new { UserName = workflowUser, WorkflowTypeId = workflowTypeId, UserWorkflowSteps = userWorkflowSteps };

            var returnUserWorkflowSteps = ApiUtility.ExecuteApiPostWithContentTo<IEnumerable<UserWorkflowStep>>(_reqApiEndpoint, SaveWorkflowMethod, new Dictionary<string, string> { { "updater", userName } }, userWorkflowDTO);

            return returnUserWorkflowSteps;
        }

        public User GetUser(string accountName)
        {
            return ApiUtility.ExecuteApiGetTo<User>(_reqApiEndpoint, GetEProUserMethod, new Dictionary<string, string> { { "accountName", accountName } });
        }

        public User GetUserWithoutDomain(string accountName)
        {
            return ApiUtility.ExecuteApiGetTo<User>(_reqApiEndpoint, GetEProUserWithoutDomainMethod, new Dictionary<string, string> { { "accountName", accountName } });
        }

        public void UpdateUsersName(string domain, string userName)
        {
            var domainSlashUserName = $"{domain}/{userName}";
            var userProfile = GetUserProfile(domain, userName);
            if (userProfile != null)
            {
                ApiUtility.ExecuteApiPostWithContentTo<object>(_reqApiEndpoint, PostEProUpdateUserName, new Dictionary<string, string>
                                                                {
                                                                    { "accountName", domainSlashUserName },
                                                                    { "firstName", userProfile.FirstName },
                                                                    { "lastName", userProfile.LastName }
                                                                }, null);
            }
        }

        public UserEditDTO GetUserEditUsers(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID)
        {
            var userEditDto = ApiUtility.ExecuteApiPostWithContentTo<UserEditDTO>(_reqApiEndpoint, GetUserEditUsersMethod, null, new UserReportInfoRequestDTO
            {
                UserName = userName,
                COID = COID,
                Users = usersNames
            });

            //commented below lines and added contion in parent method to remove vender user as we have now vendor role uer implemented.
            //userEditDto.Approvers = userEditDto.Approvers.Where(x => !UserHasVendorPart(x.User.AccountName));
            //userEditDto.NonApprovers = userEditDto.NonApprovers.Where(x => !UserHasVendorPart(x.AccountName));

            return userEditDto;
        }

        public void SaveUserEditInfo(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO)
        {
            foreach (var userWorkflow in saveUserEditInfoDTO.workflows)
            {
                userWorkflow.UserWorkflowSteps = RemoveUsersandApprovers(userWorkflow.UserWorkflowSteps, userName);
            }
            ApiUtility.ExecuteApiPostWithContentTo<object>(_reqApiEndpoint, SaveUserInfoMethod, new Dictionary<string, string> { { "userName", userName } }, saveUserEditInfoDTO);
        }

        public IEnumerable<UserProfile> GetUsersWithRoleAndSOC(string roleId, SpanOfControl spanOfControl)
        {
            if (string.IsNullOrWhiteSpace(roleId))
            {
                return null;
            }

            if (spanOfControl == null)
            {
                return null;
            }
            try
            {
                var token = AddSecurityToken();
                var generatedUrl = _newSecurityEndpoint + "roles/" + roleId + "/objectId/" + spanOfControl.ObjectId + "/hierarchyType/" + spanOfControl.Hierarchy.ToInt().ToString() + "/applicationId/" + _appKey + "/withDetails";
                var usrList = ApiUtility.ExecuteSecurityTokenApiGetTo<List<UserProfile>>(generatedUrl, null, token);

                SetEmailDomainForVendors(ref usrList);
                return usrList;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error retrieving users with role {roleId} and Span of Control {spanOfControl.ObjectId}", ex);
                throw;
            }
        }

        private void SetEmailDomainForVendors(ref List<UserProfile> userList)
        {
            foreach (var user in userList)
            {
                if (user.UserType.ToLower() == "vendor")
                {
                    //parses out email domain after @ and assigns to user.Domain
                    user.Domain = user.Email.Split('@')[1];
                }
            }
        }

        public IEnumerable<UserProfile> GetEProcurementUserProfiles(string coid)
        {
            var userProfiles = new List<UserProfile>();
            var userProfileGroups = new List<List<UserProfile>>();

            foreach (var role in PassUtility.ProcurementRoles.Values)
            {
                var hierarchy = OrganizationalLevelHierarchyType.Facility;
                var soc = new SpanOfControl { Hierarchy = hierarchy, ObjectId = coid };

                var usersWithRoleAndSoc = new List<UserProfile>();

                try
                {
                    usersWithRoleAndSoc = GetUsersWithRoleAndSOC(role, soc).ToList();
                }
                catch (Exception ex)
                {
                    Logger.Error(ex.Message);
                    //break;
                }
                userProfileGroups.Add(usersWithRoleAndSoc);
            }

            if (userProfileGroups.Count > 0)
            {
                userProfiles = userProfileGroups[0];
            }

            if (userProfileGroups.Count > 1)
            {
                for (var i = 1; i < userProfileGroups.Count; i++)
                {
                    userProfiles = userProfiles.Union(userProfileGroups[i]).ToList();
                }
            }

            return userProfiles;
        }

        public UserProfile GetUserProfile(string domainSlashUserName)
        {
            if (string.IsNullOrWhiteSpace(domainSlashUserName) || domainSlashUserName.IndexOf('/') == 0)
            {
                return null;
            }


            var nameParts = domainSlashUserName.Split('/');

            if (nameParts == null || nameParts.Count() < 2)
            {
                throw new ArgumentException("Invalid domainSlashUserName passed, no slash found, PARAMETER = " + domainSlashUserName);
            }

            return GetUserProfile(nameParts[0], nameParts[1]);
        }
        private List<UserRoleNew> GetUserRoles(string userId, string appId = "")
        {
            userId = userId.Contains('/') ? userId.Split('/')[1] : userId;
            var token = AddSecurityToken();
            var generatedUrl = _newSecurityEndpoint + "users/" + userId + "/applications/" + _appKey + "/roles";
            var roleList = ApiUtility.ExecuteSecurityTokenApiGetTo<List<UserRoleNew>>(generatedUrl, null, token);
            return roleList;
        }

        public void SetEnvironmentDomain(ref string domain)
        {
            if (_useDevelopmentDomain && (string.Equals(domain, "HCA", StringComparison.CurrentCultureIgnoreCase) || string.Equals(domain, "HCAQA", StringComparison.CurrentCultureIgnoreCase)))
                domain = "HCADEV";

            if (_useQaDomain && (string.Equals(domain, "HCA", StringComparison.CurrentCultureIgnoreCase) || string.Equals(domain, "HCADEV", StringComparison.CurrentCultureIgnoreCase)))
                domain = "HCAQA";
        }

        public string UpdateDomainByEnvironment(string userName)
        {
            var domainSlashUsername = userName.Split('/');
            var domain = domainSlashUsername[0];
            var username = domainSlashUsername[1];

            SetEnvironmentDomain(ref domain);

            return $"{domain}/{username}";
        }

        public UserProfile GetUserProfile(string domain, string userName)
        {
            SetEnvironmentDomain(ref domain);

            try
            {
                var token = AddSecurityToken();
                // Ensure the username is in lowercase when building the key for caching to prevent duplicate keys.
                var url = _newSecurityEndpoint + "users/" + userName.ToLower() + "/applications/" + _appKey;

                ICacheManager<Object> cacheManager = new RedisCacheManager<object>();
                ICacheProvider cacheProvider = new CacheProvider(cacheManager);
                var userInfo = cacheProvider.ProvideUserProfile(url, token);

                if (userInfo.UserType.ToLower() == "vendor")
                {
                    //parses out email domain after @ and assigns to user.Domain
                    userInfo.Domain = userInfo.Email.Split('@')[1];
                }

                userInfo = SetUsersAsVendorUsers(new List<UserProfile> { userInfo }).FirstOrDefault();
                //Ensure SoC is properly established 
                if (userInfo.SpanOfControl == null)
                {
                    userInfo.SpanOfControl = new List<SpanOfControl>();
                }

                if (userInfo.VendorAffils == null)
                {
                    userInfo.VendorAffils = new List<Vendor>();
                }

                if (userInfo.SpanOfControl.Any())
                {
                    userInfo.SpanOfControl.AddRange(_testCoidSpanOfControls);
                }

                return userInfo;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error retrieving user profile for specified user: Domain {domain} , UserName {userName}", ex);
                return null;
            }
        }

        public string AddSecurityToken()
        {
            var securityService = new TokenGeneratorService();
            var token = securityService.GetSmartClientAuthToken<dynamic>();
            string bearerToken = "Bearer " + token.access_token;
            if (HttpContext.Current != null)
                HttpContext.Current.Request.Headers["Authorization"] = bearerToken;
            return bearerToken;
        }

        public Profile GetProfile(string domainSlashUserName)
        {
            if (string.IsNullOrWhiteSpace(domainSlashUserName) || domainSlashUserName.IndexOf('/') == 0)
            {
                return null;
            }

            var nameParts = domainSlashUserName.Split('/');

            if (nameParts == null || nameParts.Count() < 2)
            {
                return null;
            }

            return GetSecurityProfile(nameParts[0], nameParts[1]);
        }

        public Profile GetProfile(string domain, string userName)
        {

            SetEnvironmentDomain(ref domain);

            try
            {
                return ApiUtility.ExecuteApiGetTo<Profile>(_profileEndpoint, GetProfileMethod, new Dictionary<string, string>
                {
                                                                                { "domain", domain },
                                                                                { "samAccountName", userName }
                                                                            });
            }
            catch (Exception ex)
            {
                Logger.Error(string.Format("Error retrieving profile for specified user: Domain {0} , UserName {1}", domain, userName), ex);
                return null;
            }
        }

        public Profile GetSecurityProfile(string domain, string userName)
        {
            SetEnvironmentDomain(ref domain);

            try
            {
                var parameters = new Dictionary<string, object>
                {
                    { "domain", domain },
                    { "userName", userName },
                    { "appId", _appKey },
                    { "withDetails", true }
                };
                var user = ApiUtility.ExecuteSecurityApiPostTo<Profile>(_newSecurityEndpoint + PostUserDetails,
                    parameters, AddSecurityToken());
                return user;
            }
            catch (Exception ex)
            {
                Logger.Error(
                    $"Error retrieving Security profile for specified user: Domain {domain} , UserName {userName}", ex);
                return null;
            }
        }

        public IEnumerable<AppPart> GetUserParts(string domain, string username)
        {
            SetEnvironmentDomain(ref domain);

            var newRoles = GetUserRoles(username, "");
            var userParts = GenerateAppParts(newRoles);

            return userParts;
        }

        public IEnumerable<AppPart> GetUserParts(string domainSlashUserName)
        {
            if (string.IsNullOrWhiteSpace(domainSlashUserName) || domainSlashUserName.IndexOf('/') == 0)
            {
                return null;
            }

            var nameParts = domainSlashUserName.Split('/');

            if (nameParts == null || nameParts.Count() < 2)
            {
                return null;
            }

            return GetUserParts(nameParts[0], nameParts[1]);
        }
        private List<AppPart> GenerateAppParts(List<UserRoleNew> UserRoles)
        {
            List<AppPart> AppParts = new List<AppPart>();
            foreach (var role in UserRoles)
            {
                switch (role.Name)
                {
                    //FIXME: These hard-coded values need to be cleaned up.........
                    case UserProfileRoleNames.EProcApproverRoleName:
                        var partPA = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":3,\"ApplicationSystemId\":0,\"Name\":\"MyApprovals\",\"AppPartType\":2},{\"Id\":4,\"ApplicationSystemId\":0,\"Name\":\"MyItemRequests\",\"AppPartType\":0},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":33,\"ApplicationSystemId\":0,\"Name\":\"AdHocReviewer\",\"AppPartType\":15}]");
                        AppParts.AddRange(partPA);
                        break;
                    case UserProfileRoleNames.EProcAdministratorRoleName:
                        var partPAd = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":22,\"ApplicationSystemId\":0,\"Name\":\"AdminScreen\",\"AppPartType\":9},{\"Id\":23,\"ApplicationSystemId\":0,\"Name\":\"ManageWorkflowNotifications\",\"AppPartType\":10},{\"Id\":24,\"ApplicationSystemId\":0,\"Name\":\"UserWorkflowEdit\",\"AppPartType\":11},{\"Id\":25,\"ApplicationSystemId\":0,\"Name\":\"ApproverEdit\",\"AppPartType\":12}]");
                        AppParts.AddRange(partPAd);
                        break;
                    case UserProfileRoleNames.EProcRequisitionerRoleName:
                        var partReq = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":2,\"ApplicationSystemId\":0,\"Name\":\"MyRequisitions\",\"AppPartType\":1},{\"Id\":4,\"ApplicationSystemId\":0,\"Name\":\"MyItemRequests\",\"AppPartType\":0},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":19,\"ApplicationSystemId\":0,\"Name\":\"SearchItems\",\"AppPartType\":6}]");
                        AppParts.AddRange(partReq);
                        break;
                    case UserProfileRoleNames.EProcSPRRequisitionerRoleName:
                        var partspr = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":2,\"ApplicationSystemId\":0,\"Name\":\"MyRequisitions\",\"AppPartType\":1},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":19,\"ApplicationSystemId\":0,\"Name\":\"SearchItems\",\"AppPartType\":6},{\"Id\":20,\"ApplicationSystemId\":0,\"Name\":\"CreateSPR\",\"AppPartType\":7},{\"Id\":21,\"ApplicationSystemId\":0,\"Name\":\"CreateCapitalReq\",\"AppPartType\":8},{\"Id\":26,\"ApplicationSystemId\":0,\"Name\":\"PunchOutReq\",\"AppPartType\":13},{\"Id\":29,\"ApplicationSystemId\":0,\"Name\":\"SPRPARItems\",\"AppPartType\":14},{\"Id\":34,\"ApplicationSystemId\":0,\"Name\":\"CreateRushReq\",\"AppPartType\":16}]");
                        AppParts.AddRange(partspr);
                        break;
                    case UserProfileRoleNames.EProcReviewerRoleName:
                        var partPR = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":5,\"ApplicationSystemId\":0,\"Name\":\"MyReports\",\"AppPartType\":5},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4}]");
                        AppParts.AddRange(partPR);
                        break;
                    case UserProfileRoleNames.EProcVendorRoleName:
                        var partVB = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":17,\"ApplicationSystemId\":0,\"Name\":\"VendorBillOnly\"},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"SearchItems\"}]");
                        AppParts.AddRange(partVB);
                        break;
                    case UserProfileRoleNames.EProcBillOnlyReviewerRoleName:
                        var partBillOnlyReviewer = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":18,\"ApplicationSystemId\":0,\"Name\":\"BillOnlyReview\"},{\"Id\":18,\"ApplicationSystemId\":0,\"Name\":\"BillOnlyReview\"}]");
                        AppParts.AddRange(partBillOnlyReviewer);
                        break;
                    default:
                        break;

                }
            }
            return AppParts.DistinctBy(d => d.Id).ToList();
        }
        public IEnumerable<WorkflowType> GetAllWorkflowTypes()
        {
            return ApiUtility.ExecuteApiGetTo<IEnumerable<WorkflowType>>(_reqApiEndpoint, GetWorkflowTypesMethod, null);
        }

        public IEnumerable<WorkflowType> GetAllUserWorkflowTypes()
        {
            return ApiUtility.ExecuteApiGetTo<IEnumerable<WorkflowType>>(_reqApiEndpoint, GetUserWorkflowTypesMethod, null);
        }

        public WorkflowValidationDTO ValidateUserWorkflow(string userName, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal = default(decimal?))
        {
            var parameters = new Dictionary<string, string>
            {
                { "userName", userName },
                { "workflowType", ((int)workflowType).ToString() },
                { "COID", COID }
            };

            if (requisitionTotal != null)
            {
                parameters.Add("requisitionTotal", requisitionTotal.ToString());
            }

            return ApiUtility.ExecuteApiGetTo<WorkflowValidationDTO>(_reqApiEndpoint, ValidateUserWorkflowMethod, parameters);
        }

        public ValidationOfUserWorkflowsDTO GetValidationOfUserWorkflows(ValidateUserWorkflowsRequestDTO validateUserWorkflowsRequestDTO)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<ValidationOfUserWorkflowsDTO>(_reqApiEndpoint, GetValidationOfUserWorkflowsMethod, null, validateUserWorkflowsRequestDTO);
        }

        public IEnumerable<UserReportInfoDTO> RetrieveUserReportInfo(UserReportInfoRequestDTO userReportInfoRequestDTO)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<IEnumerable<UserReportInfoDTO>>(_reqApiEndpoint, RetrieveUserReportInfoMethod, null, userReportInfoRequestDTO);
        }

        //TODO : This method will be removed as we implement the new role features
        List<UserProfile> SetUsersAsVendorUsers(List<UserProfile> users)
        {
            foreach (var vendor in users)
            {
                if (vendor.Roles.Any(x => x.RoleName == Constants.UserProfileRoleNames.EProcVendorRoleName))
                {
                    vendor.VendorAffils = new List<Vendor>();

                    if (vendor.vendors.Count > 0)
                    {
                        foreach (var v in vendor.vendors)
                        {
                            vendor.VendorAffils.Add(new Vendor
                            {
                                Id = v.vendorId,
                                Name = v.vendorName
                            });
                        }
                    }
                }
            }

            return users;
        }

        public bool UserHasVendorPart(string username)
        {
            //checked user is vender user or not based on Role
            var userRoles = GetUserRoles(username, "");
            return userRoles.Any(x => x.RoleName == Constants.UserProfileRoleNames.EProcVendorRoleName);
        }

        IEnumerable<UserWorkflowStep> RemoveUsersandApprovers(IEnumerable<UserWorkflowStep> userWorkflowSteps, string userName)
        {
            if (userWorkflowSteps == null)
            {
                userWorkflowSteps = new List<UserWorkflowStep>();
            }
            int index = 1;
            foreach (var userWorkflowStep in userWorkflowSteps)
            {
                //Don't save users and approves, just steps
                userWorkflowStep.User = null;
                userWorkflowStep.Approver = null;
                userWorkflowStep.Step = index++;

                if (string.IsNullOrWhiteSpace(userWorkflowStep.CreatedBy))
                {
                    userWorkflowStep.CreatedBy = userName;
                    userWorkflowStep.CreateDate = DateTime.Now;
                }
            }

            return userWorkflowSteps;
        }

        private void GetUserVPROBadgeInDetails(ref List<string> coidList, ref int VProId, ref VPROReturnObject userList, VPROBadgeInRequest request)
        {
            try
            {

                var parameters = new Dictionary<string, string>
                {
                    { "coid", request.coid },
                    { "userName", request.userName },
                    { "APPID", _appKey },
                    { "REQUISITIONDATETIME", request.procedureDateTime }
                };

                var token = AddSecurityToken();
                var generatedUrl = _newSecurityEndpoint + "vpro/GetUserVPROBadgeInDetailsAsync/";
                userList = ApiUtility.ExecuteSecurityTokenApiPostTo<VPROReturnObject>(generatedUrl, parameters, token);

                if (userList.Content != null)
                {
                    foreach (var item in userList.Content)
                    {
                        if (item.isRequisitionAccessAvailable)
                        {
                            coidList.Add(item.coid);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error retrieving data from GetUserVPROBadgeInDetailsAsync", ex);
            }
        }

        public VPROResponseFiltered GetAndStoreVProBadgeInDetails(VPROBadgeInRequest request)
        {
            var VProId = 0;
            List<string> coidList = new List<string>();
            VPROReturnObject userList = new VPROReturnObject();
            GetUserVPROBadgeInDetails(ref coidList, ref VProId, ref userList, request);
            int statusId = (userList?.Status?.statusCode == 500 || userList?.Status?.statusCode == null ) ? 2 : (coidList.Contains(request.coid) ? 1 : 0);

            var newRecordRequest = new
            {
                Id = request.id,
                RequisitionId = request.requisitionId,
                BadgeInStatusId = statusId
            };

            try
            {
                if (request.requisitionId != 0)
                {
                    var VproRecordResponse = ApiUtility.ExecuteApiPostWithContentTo<RequisitionVProBadgeLog>(_reqApiEndpoint, CreateVProBadgeLog, null, newRecordRequest);
                    VProId = VproRecordResponse.Id;
                }

                VPROResponseFiltered result = new VPROResponseFiltered
                {
                    Id = VProId,
                    Coid = coidList,
                    VproBadgeVerified = statusId
                };

                return result;

            }
            catch (Exception ex)
            {
                Logger.Error("Error storing data to RequisitionVProBadgeLogs Table", ex);
                return null;
            }
        }

        public List<UserProfile> GetApproverProfileListForWorkflow(IEnumerable<UserWorkflowStep> steps)
        {
            try
            {
                string token = AddSecurityToken();
                ICacheManager<Object> cacheManager = new RedisCacheManager<object>();
                ICacheProvider cacheProvider = new CacheProvider(cacheManager);
                List<UserProfile> approverProfileList = new List<UserProfile>();
                List<string> userNameList = new List<string>();
                foreach (var userName in steps.Select(x => x.Approver?.User?.AccountName).Where(x => !string.IsNullOrWhiteSpace(x)))
                {
                    string userNameWithoutDomain = userName.Split('/')[1].ToLowerInvariant();
                    userNameList.Add(userNameWithoutDomain);
                }
                //process list returned from cache provider method
               List<UserProfile> providedProfiles = cacheProvider.ProvideApproverProfileList(userNameList, token);
                approverProfileList = ProcessUserProfilesForWorkflowApprovers(providedProfiles ?? new List<UserProfile>());
                return approverProfileList;
            }
            catch (Exception ex)
            {
                Logger.Error("Error in GetUserProfileListForWorkflow", ex);
                return new List<UserProfile>();
            }
        }

        public List<UserProfile> ProcessFacilityUserProfilesForWorkflowApprovers(List<UserProfile> facilityApproverProfileList)
        {
            return ProcessUserProfilesForWorkflowApprovers(facilityApproverProfileList);
        }

        private List<UserProfile> ProcessUserProfilesForWorkflowApprovers(List<UserProfile> approverProfileList)
        {
            foreach (UserProfile approverProfile in approverProfileList)
            {
                if (approverProfile.SpanOfControl == null)
                {
                    approverProfile.SpanOfControl = new List<SpanOfControl>();
                }

                if (approverProfile.VendorAffils == null)
                {
                    approverProfile.VendorAffils = new List<Vendor>();
                }

                if (approverProfile.SpanOfControl.Any())
                {
                    approverProfile.SpanOfControl.AddRange(_testCoidSpanOfControls);
                }
            }
            return approverProfileList;
        }
    }
}
