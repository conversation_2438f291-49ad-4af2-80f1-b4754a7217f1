using eProcurementServices.Domain.DTO.BillOnlyReview;
using System.Collections.Generic;
using System.Linq;

namespace eProcurementServices.Domain.Model.Reports.MigraDoc
{
    public static class BillOnlyReviewItemsDisplayMapper
    {
        public static BillOnlyReviewItemsDisplay Map(BillOnlyReviewItemDAO reqItem)
        {
            var display = new BillOnlyReviewItemsDisplay();
            if (reqItem.VboHoldItemConversion != null)
            {
                MapFromVboHoldItemConversion(display, reqItem);
            }
            else if (reqItem.SPRDetail != null)
            {
                MapFromSPRDetail(display, reqItem);
            }
            else
            {
                MapFromDAO(display, reqItem);
            }

            // Map common fields that are available regardless of the source
            MapCommonFields(display, reqItem);

            return display;
        }

        private static void MapCommonFields(BillOnlyReviewItemsDisplay display, BillOnlyReviewItemDAO reqItem)
        {
            display.PONumber = reqItem.PONumber?.ToString() ?? "N/A";
            display.ParentSystemId = BillOnlyReviewItemsDisplay.OrDefault(reqItem.ParentSystemId, "N/A");

            // QuantityFulfilled - only show for specific status types matching frontend logic:
            // ng-if="readOnly && (item.RequisitionItemStatusType == reqItemStatusTypesEnum.ITEMRECEIVED.Id ||
            //         item.RequisitionItemStatusType == reqItemStatusTypesEnum.REQFILLED.Id ||
            //         item.RequisitionItemStatusType == reqItemStatusTypesEnum.NOPARTIALFILL.Id) &&
            //         (!item.IsSPR || isBillOnlyInfoAvailable || (item.IsSPR && item.Item.IsStock))"
            bool shouldShowQuantityFulfilled = (reqItem.StatusTypeId == 16 || reqItem.StatusTypeId == 19 || reqItem.StatusTypeId == 24) && // ITEMRECEIVED, REQFILLED, NOPARTIALFILL
                                               reqItem.QuantityFulfilled.HasValue;
            display.QuantityFulfilled = shouldShowQuantityFulfilled ? reqItem.QuantityFulfilled.ToString() : "N/A";

            display.IsRush = reqItem.IsRush ? "Yes" : "No";
            display.UpchargeAmount = reqItem.UpchargeAmount?.ToString("0.00") ?? "0.00";
            display.OnContract = reqItem.OnContract.HasValue ? (reqItem.OnContract.Value ? "Yes" : "No") : "N/A";
            display.PartsWarrantyMonths = reqItem.PartsWarrantyMonths?.ToString() ?? "N/A";
            display.LaborWarrantyMonths = reqItem.LaborWarrantyMonths?.ToString() ?? "N/A";

            // Project Number - only show when SPRDetail.BudgetNumber exists AND VboHoldItemConversion is null
            // This matches frontend logic: ng-if="item.SPRDetail.BudgetNumber && item.VboHoldItemConversion == null"
            display.ProjectNumber = (reqItem.VboHoldItemConversion == null && !string.IsNullOrEmpty(reqItem.ProjectNumber))
                ? reqItem.ProjectNumber
                : "N/A";
        }

        private static void MapFromVboHoldItemConversion(BillOnlyReviewItemsDisplay display, BillOnlyReviewItemDAO reqItem)
        {
            var item = reqItem.VboHoldItemConversion?.ItemDetails?.Item;
            display.ItemNumber = BillOnlyReviewItemsDisplay.OrDefault(reqItem.VboHoldItemConversion?.ItemDetails?.ItemId.ToString(), "No Item ID");
            display.ItemDescription = BillOnlyReviewItemsDisplay.OrDefault(item?.Description, "No Description");
            display.Par = BillOnlyReviewItemsDisplay.OrDefault(reqItem.VboHoldItemConversion?.ItemDetails?.ParId, "No Par ID");
            display.VendorName = BillOnlyReviewItemsDisplay.OrDefault(item != null ? $"{item.MfgVendName} ({item.MfgVendNumber})" : null, "No Vendor Name");
            display.ReorderNumber = BillOnlyReviewItemsDisplay.OrDefault(item?.ReorderNumber, "No Reorder Number");
            display.CatalogNumber = BillOnlyReviewItemsDisplay.OrDefault(item?.ManufacturerCatalogNumber, "No Catalog Number");
            display.ProcedureCode = BillOnlyReviewItemsDisplay.OrDefault(item?.ProcCode, "No Procedure Code");
            display.Chargeable = BillOnlyReviewItemsDisplay.ToYesNo(item?.Chargeable);
            display.Serial = reqItem.Serial ?? new List<string> { "N/A" };
            display.Location = BillOnlyReviewItemsDisplay.OrDefault(reqItem.VboHoldItemConversion?.ItemDetails?.Location, "No Location");
            display.Stock = BillOnlyReviewItemsDisplay.ToYesNo(item?.IsStock);
            display.Discount = reqItem.Discount.HasValue ? reqItem.Discount.Value.ToString("0.00") : "0.00";
            display.GL = BillOnlyReviewItemsDisplay.ToStringOrNA(reqItem.VboHoldItemConversion?.ItemDetails?.GLAccount);
            display.UOMCode = BillOnlyReviewItemsDisplay.OrDefault(item?.UOM, "No UOM");
            display.Lot = (reqItem.Lot != null && reqItem.Lot.Any()) ? reqItem.Lot : new List<string> { "N/A" };
            display.Unit = BillOnlyReviewItemsDisplay.UnitCostCalculation(reqItem).Value.ToString("0.00");
            display.Total = BillOnlyReviewItemsDisplay.TotalItemCostCalculation(reqItem).ToString("0.00");
            display.Quantity = reqItem.QuantityToOrder.ToString();
        }

        private static void MapFromSPRDetail(BillOnlyReviewItemsDisplay display, BillOnlyReviewItemDAO reqItem)
        {
            var spr = reqItem?.SPRDetail;
            display.ItemNumber = BillOnlyReviewItemsDisplay.OrDefault(reqItem.ItemId?.ToString(), "No Item ID");
            display.ItemDescription = BillOnlyReviewItemsDisplay.OrDefault(spr?.ItemDescription, "No Description");
            display.VendorName = BillOnlyReviewItemsDisplay.OrDefault(spr != null ? $"{spr.Vendor?.Name}({spr.Vendor?.Id})" : null, "No Vendor Name");
            display.ReorderNumber = BillOnlyReviewItemsDisplay.OrDefault(spr?.PartNumber, "No Reorder Number");
            display.CatalogNumber = BillOnlyReviewItemsDisplay.OrDefault(reqItem?.CatalogNumber, "No Catalog Number");
            display.ProcedureCode = "No Procedure Code";
            display.Chargeable = "No";
            display.GL = BillOnlyReviewItemsDisplay.OrDefault(spr?.GeneralLedgerCode, "No GL");
            display.UOMCode = BillOnlyReviewItemsDisplay.OrDefault(spr?.UOM?.Code, "No UOM");
            display.Lot = (reqItem.Lot != null && reqItem.Lot.Any()) ? reqItem.Lot : new List<string> { "N/A" };
            display.Serial = reqItem.Serial ?? new List<string> { "N/A" };
            display.Par = "No Par ID";
            display.Location = "No Location";
            display.Stock = "No";
            display.Discount = reqItem.Discount.HasValue ? reqItem.Discount.Value.ToString("0.00") : "0.00";
            display.Unit = BillOnlyReviewItemsDisplay.UnitCostCalculation(reqItem).Value.ToString("0.00");
            display.Total = BillOnlyReviewItemsDisplay.TotalItemCostCalculation(reqItem).ToString("0.00");
            display.Quantity = reqItem.QuantityToOrder.ToString();
        }

        private static void MapFromDAO(BillOnlyReviewItemsDisplay display, BillOnlyReviewItemDAO reqItem)
        {
            display.ItemNumber = BillOnlyReviewItemsDisplay.OrDefault(reqItem.ItemId, "No Item ID");
            display.ItemDescription = BillOnlyReviewItemsDisplay.OrDefault(reqItem.Description, "No Description");
            display.VendorName = BillOnlyReviewItemsDisplay.OrDefault(reqItem != null ? $"{reqItem.VendorName}({reqItem.VendorNumber})" : null, "No Vendor Name");
            display.ReorderNumber = BillOnlyReviewItemsDisplay.OrDefault(reqItem.ReorderNumber, "No Reorder Number");
            display.CatalogNumber = BillOnlyReviewItemsDisplay.OrDefault(reqItem.CatalogNumber, "No Catalog Number");
            display.Chargeable = BillOnlyReviewItemsDisplay.ToYesNo(reqItem?.Item?.Chargeable);
            display.Lot = (reqItem.Lot != null && reqItem.Lot.Any()) ? reqItem.Lot : new List<string> { "N/A" };
            display.Serial = reqItem.Serial ?? new List<string> { "N/A" };
            display.Par = BillOnlyReviewItemsDisplay.OrDefault(reqItem?.ParItem?.ParId, "No Par ID");
            display.Location = BillOnlyReviewItemsDisplay.OrDefault(reqItem.ParLocation, "No Location");
            display.Stock = BillOnlyReviewItemsDisplay.ToYesNo(reqItem?.Item?.IsStock);
            display.Discount = reqItem.Discount.HasValue ? reqItem.Discount.Value.ToString("0.00") : "0.00";
            display.ProcedureCode = BillOnlyReviewItemsDisplay.OrDefault(reqItem.Item?.ProcCode, "No Procedure Code");
            display.Unit = BillOnlyReviewItemsDisplay.UnitCostCalculation(reqItem).Value.ToString("0.00");
            display.Total = BillOnlyReviewItemsDisplay.TotalItemCostCalculation(reqItem).ToString("0.00");
            display.GL = BillOnlyReviewItemsDisplay.OrDefault(reqItem.GL, "No GL");
            display.UOMCode = BillOnlyReviewItemsDisplay.OrDefault(reqItem.UOMCode, "No UOM");
            display.Quantity = reqItem.QuantityToOrder.ToString();
        }
    }
}
