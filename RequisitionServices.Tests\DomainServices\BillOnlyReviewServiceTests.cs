﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories;
using System.Collections.Generic;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class BillOnlyReviewServiceTests
    {
        private static Mock<IBillOnlyReviewService> mockIBillOnlyReviewService;
        private static Mock<IRequisitionService> mockRequisitionService;
        private static Mock<IBillOnlyReviewRepository> mockBillOnlyReviewRepository;
        private static Mock<ISmartItemService> mockSmartItemService;
        private static Mock<IParService> mockParService;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            mockIBillOnlyReviewService = new Mock<IBillOnlyReviewService>();
            mockRequisitionService = new Mock<IRequisitionService>();
            mockBillOnlyReviewRepository = new Mock<IBillOnlyReviewRepository>();
            mockSmartItemService = new Mock<ISmartItemService>();
            mockParService = new Mock<IParService>();
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            mockIBillOnlyReviewService = null;
            mockRequisitionService = null;
            mockBillOnlyReviewRepository = null;
            mockSmartItemService = null;
            mockParService = null;
        }
    }
}
