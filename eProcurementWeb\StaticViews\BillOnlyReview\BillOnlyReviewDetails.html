﻿<div class="mainViewItem">
    <div class="mainView-header">
        <p class="mainView-title">
            <span class="reqName" ng-bind="pageTitle()"> </span>
            <span class="reqStatus" ng-show="requisition.RequisitionId > 0" ng-bind-html="requisition.RequisitionStatusType | requisitionStatus: requisition.RequisitionStatus"></span>
            <dso-requisition-submission-type info="requisition" details-view="true" list-view="false"></dso-requisition-submission-type>
        </p>
    </div>
    <div name="requisitionForm">
        <div class="row">
            <div class="showWhenPrint">
                <div class="summaryArea">
                    <h3 class="marginBottom0">Summary</h3>
                    <div>Total Items: {{sumItems(requisition.RequisitionItems)}}</div>
                    <div>Total Quantities: {{sumQuantity(requisition.RequisitionItems)}}</div>
                    <div>
                        Total Amount:
                        <span ng-if="sprItemsExist(requisition.RequisitionItems)" ng-bind="totalReqAmount(requisition) | currency: (requisition.Facility.SmartCountryCode | currencyDisplayFilter)"></span>
                        <span ng-if="!sprItemsExist(requisition.RequisitionItems)">N/A</span>
                    </div>
                    <div>Company: {{requisition.Facility.CompanyName}}</div>
                    <div>Facility: {{requisition.Facility.Name}}</div>
                    <div>Department: {{requisition.Department.Description}}</div>
                    <div ng-show="requisition.RequisitionId > 0">Requisition By: {{requisition.CreatedByFullName}}</div>
                    <div ng-show="requisition.RequisitionId > 0">Requisition On: {{requisition.RequisitionDate | date: 'short'}}</div>
                    <div class="form-group" ng-if="requisition.Comments != null">
                        <span>Comments: </span>
                        <span ng-if="requisition.CaseNumber">{{vendorBillOnlyHeaderString}} {{requisition.CaseNumber}} {{vendorBillOnlyDividerString}} </span>
                        <span>{{requisition.VisibleComments}}</span>
                    </div>
                    <div ng-show="requisition.IsVendor && featureFlags.FFVPROBadgeInFE"> Vendor Badged In: {{ requisition.VProBadgeLog.BadgeInStatusId | badgeStatus }}</div>
                </div>
            </div>
            <div class="col-lg-9 col-md-9 col-sm-12 col-xs-12">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="detailsContainer">
                            <div class="NonItemContainer">
                                <div class="row dropdownHeader">
                                    <div class="col-lg-7 col-xs-10 form-inline paddingRight0">
                                        <div class="form-group dropdown inlineBlock" dropdown keyboard-nav>
                                            <button event-focus="click" event-focus-id="facilityFilterInput" type="button" class="facilityDropdownButton btn dropdown-toggle marginBottom10" dropdown-toggle aria-expanded="false" disabled>
                                                Facility {{requisition.COID}} <span class="caret"></span>
                                            </button>
                                           
                                        </div>
                                        <div class="form-group dropdown inlineBlock" dropdown keyboard-nav>
                                            <button event-focus="click" event-focus-id="deptFilterInput" type="button" class="departmentDropdownButton btn dropdown-toggle marginBottom10" dropdown-toggle aria-expanded="false" disabled>
                                                Department {{requisition.DepartmentId}} <span class="caret"></span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-lg-5 col-md-12 smallerResolutionMarginTop">
                                        <div class="inlineBlock pull-right marginBottom15">
                                            <a id="printReqDetailButton" class="btn purple iconButton" onclick="window.print()" tooltip="Print" tooltip-append-to-body="true">
                                                <span class="glyphicon glyphicon-print"></span>
                                            </a>
                                            <a id="historyReqDetailButton" class="btn gray iconButton" ng-click="viewHistory()" tooltip="History" tooltip-append-to-body="true" ng-disabled="isLegacy">
                                                <span class="glyphicon glyphicon-inbox"></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div ng-if="requisition.RequisitionStatusType != 4 && requisition.RequisitionStatusType != 2 && (requisition.RequisitionType == 3 || requisition.RequisitionType == 4 || requisition.RequisitionType == 6) && requisition.RequisitionItems.length != 0" class="widgetHeader widgetHeaderEmpty"></div>
                                <div ng-show="requisition.RequisitionStatusType == 4 || requisition.RequisitionStatusType == 2" class="widgetContainer">
                                    <div class="widgetHeader" style="border-bottom:0;">
                                        <div class="row">
                                            <div class="col-lg-7 col-md-7 col-sm-7 col-xs-12 form-inline">
                                                <div class="form-group">
                                                    <input type="text" ng-model="filterSearchText" placeholder="Filter Items" ng-model-options="{debounce:250}" class="form-control input-sm" />
                                                    <a href tabindex="-1" ng-click="clearFilter()">Clear Filter</a>
                                                </div>
                                            </div>
                                            <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
                                                <div class="pull-right form-inline phoneNoPullRight">
                                                    <div class="form-group">
                                                        <strong>Sort By</strong>
                                                        <select class="form-control select-sm" tabindex="-1" ng-model="sortingFieldName" ng-change="sortChange()" ng-options="opt as opt.label for opt in sortingOptions"></select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="itemCardContainer">
                                <requisition-item-list requisition="requisition" item-list="requisition.RequisitionItemsDisplay" read-only="true" cart-view="false" approval-view="false" filter-function="filterFunction" is-rush-req="isRushReq" is-bill-only-info-available="isBillOnlyInfoAvailable" highlight-empty-fields="highlightEmptyFields" is-bill-only-review="true"></requisition-item-list>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 hideWhenPrint">
                <div bill-only-review-summary></div>
            </div>
        </div>
    </div>
</div>