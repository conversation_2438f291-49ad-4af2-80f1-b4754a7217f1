<?xml version="1.0"?>
<doc>
    <assembly>
        <name>eProcurementServices</name>
    </assembly>
    <members>
        <member name="M:eProcurementServices.Controllers.BillOnlyReviewController.GetBillOnlyReviewRequisitions(eProcurementServices.Domain.Model.BillOnlyReview.BillOnlyReviewRequest)">
             <summary>
            Retrieves bill only review requisitions based on the specified request
             </summary>
             <param name="request">The request containing search criteria</param>
             <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.AdhocReviewController">
            <summary>
            This controller is for Ad hoc reviews functionality
            </summary>    
        </member>
        <member name="M:eProcurementServices.Controllers.AdhocReviewController.GetAdhocReviewers(System.String)">
            <summary>
            Gets AdHocReviewers for User
            </summary>
            <param name="cOID"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.AdhocReviewController.RequestAdhocReview(eProcurementServices.Domain.DTO.AdhocReviewDTO)">
            <summary>
            Request Ad hoc Review
            </summary>
            <param name="adhocReview"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.AdhocReviewController.ProvideAdhocReview(eProcurementServices.Domain.DTO.AdhocReviewDTO)">
            <summary>
            Provide ad hoc review
            </summary>
            <param name="adhocReview"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.AdhocReviewController.IsAdhocReviewAllowed(System.Int32,System.String,System.Int32)">
            <summary>
            If ad hoc review is allowed for this user for the requisition
            </summary>
            <param name="adhocReviewId"></param>
            <param name="reviewer"></param>
            <param name="reqId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.AdhocReviewController.GetRequisitionAdhocReviews(System.Int32)">
            <summary>
            Gets ad hoc reviews for a requisition
            </summary>
            <param name="requisitionId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.CacheController.RefreshCache">
            <summary>
            Refresh Cache from UI
            </summary>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.ClinicalDataController">
            <summary>
            Used for pulling clinical specific data to UI
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ClinicalDataController.GetProviders(System.String)">
            <summary>
            Get a list of providers for the user/COID
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ClinicalDataController.GetPatients(System.String)">
            <summary>
            Get a list of patients for the user/COID
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ClinicalDataController.GetPatient(System.String,System.String)">
            <summary>
            Get a Patient record
            </summary>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.ConfigurationController">
            <summary>
            This controller is for user profile information retrieval
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetFacilityNotifications(System.String)">
            <summary>
            Get a list of facility notifications available to a facility
            </summary>
            <param name="cOID"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.SaveFacilityNotifications(eProcurementServices.Domain.DTO.FacilitiyNotificationsDTO)">
            <summary>
            Save facility notifications
            </summary>
            <param name="facilitiyNotificationsDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetUserEditInfo(System.String)">
            <summary>
            Gets user edit information
            A list of approvers and users that are edited in Edit User
            </summary>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.SaveUserEditInfo(eProcurementServices.Domain.DTO.SaveUserEditInfoDTO)">
            <summary>
            Saves the User's Approval amount (if applicable) and workflows (if applicable).
            </summary>
            <param name="saveUserEditInfoDTO"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.UpdateApprover(eProcurementServices.Domain.Model.Profile.Approver)">
            <summary>
            Updates just one approver.
            </summary>
            <param name="approver"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.UpdateApprovers(eProcurementServices.Domain.DTO.ApproverUpdateDTO)">
            <summary>
            Update approvers
            </summary>
            <param name="approverUpdateDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetApprover(System.String)">
            <summary>
            Get approver object for user
            </summary>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetApproverWorkflows(System.Int32,System.String)">
            <summary>
            Get approver list object for workflow
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetActiveApprovers">
            <summary>
            Get approver list object for workflow
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetUserWorkflowSteps(System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Get list of user workflow steps
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="workflowTypeId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetAllWorkflowTypes">
            <summary>
            Get list of workflow types
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetAllUserWorkflowTypes">
            <summary>
            Get list of User workflow types
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.SaveWorkflows(eProcurementServices.Domain.DTO.SaveWorkflowsDTO)">
            <summary>
            Saves all user's workflows' steps.
            </summary>
            <param name="saveWorkflowsDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.SaveUserWorkflow(eProcurementServices.Domain.DTO.UserWorkflowDTO)">
            <summary>
            Save user workflow steps
            </summary>
            <param name="userWorkflowDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.ValidateWorkflowForRequisition(System.String,eProcurementServices.Domain.Enums.WorkflowTypeEnum,System.String,System.Boolean,System.Nullable{System.Decimal})">
            <summary>
            Validates a user's workflow from database
            </summary>
            <param name="username"></param>
            <param name="workflowType"></param>
            <param name="coid">Optional: Will apply Span of Control rules against COID</param>
            <param name="isVendorRequisition"></param>
            <param name="requisitionTotal">Optional: Will apply requisition total/approval amount rules</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetValidationOfUserWorkflows(eProcurementServices.Domain.DTO.ValidateUserWorkflowsRequestDTO)">
            <summary>
            Validates all workflows for a user.
            </summary>
            <param name="validateUserWorkflowsRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetDelegatesForUser">
            <summary>
            Get a list of delegates for user
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.DeleteDelegatesForApprover(eProcurementServices.Domain.DTO.DelegateAssignmentDTO)">
            <summary>
            Delete all workflow steps that the approverId passed in has delegated
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.AssignDelegateForUser(eProcurementServices.Domain.DTO.DelegateAssignmentDTO)">
            <summary>
            Assigns a delegate to a user
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.SaveBulkApproverJob(eProcurementServices.Domain.DTO.BulkApprover)">
            <summary>
            Save Bulk Approver Remove or Exchange Data
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ConfigurationController.GetBulkApproverJobDetails">
            <summary>
            Get the list of jobs to display on Background Tasks on UI
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.DigitalSignOffController.#ctor(eProcurementServices.Domain.Interfaces.Services.IDigitalSignOffService)">
            <summary>
            Injection of services needed for Digital Sign Off process
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:eProcurementServices.Controllers.DigitalSignOffController.ActiveDirectoryValidation(System.String)" -->
        <member name="M:eProcurementServices.Controllers.IINItemController.GetIINItemById(System.String,System.String)">
            <summary>
            Get an IIN Item by Id
            </summary>
            <param name="COID"></param>
            <param name="IINitemNumber"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.LocationController.UpdateLocator(System.String,eProcurementServices.Domain.DTO.LocatorDTO)">
            <summary>
            Update Locations and return results
            </summary>
            <param name="userName"></param>
            <param name="locator"></param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.POController">
            <summary>
            This controller is for methods related to purchase orders displayed in application
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.POController.GetDetailsByPO(System.String,System.String,System.String)">
            <summary>
            Get a list of requisitions for a COID and PONumber
            </summary>
            <param name="COID"></param>
            <param name="PONumber"></param>
            <param name="stockIndicator"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.POController.GetPOConfirmationDetails(System.String,System.String,System.String)">
            <summary>
            Retrieves all Confirmation Details associated with an item in a PO
            </summary>
            <param name="COID"></param>
            <param name="PONumber"></param>
            <param name="lineNumber"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.POController.GetDetailsByDateRange(System.String,System.DateTime,System.DateTime,System.Int32)">
            <summary>
            Get PO Details by Date Range
            </summary>
            <param name="COID"></param>
            <param name="startDate"></param>
            <param name="endDate"></param>
            <param name="department"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.POController.GetPoByOptions(System.String,System.DateTime,System.DateTime,System.String,System.Int32,System.String)">
            <summary>
            Gets PODetails based on PO Type
            </summary>
            <param name="coid"></param>
            <param name="startDate"></param>
            <param name="endDate"></param>
            <param name="poType"></param>
            <param name="department"></param>
            <param name="reorderNumber"></param>
            <returns>Returns POOption</returns>
        </member>
        <member name="M:eProcurementServices.Controllers.POController.GetPoByProjectNumber(System.String,System.String)">
            <summary>
            Gets known POs associated with a project number for given COID
            </summary>
            <param name="coid"></param>
            <param name="projectNumber"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.POController.GetLinksByFileNames(eProcurementServices.Domain.DTO.FileNamesDTO)">
            <summary>
            Get Links for FileNames
            </summary>
            <param name="files"></param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.PunchOutController">
            <summary>
            Controller to receive PunchOut messages
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.PunchOutController.ReceivePunchOutOrderMessage">
            <summary>
            This is the method that receives PunchOut order message
            NOTE: this is written for CDW. This may need to be changed for others
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.PunchOutController.InitiatePunchOut(eProcurementServices.Domain.DTO.PunchOutRequestDTO)">
            <summary>
             Initiates a punch-out, returns DTO (Warnings and URL to return)
            </summary>
            <param name="punchOutRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SystemNotificationController.GetSystemNotificationTabInfo">
            <summary>
            Returns Admins granted access to populate the list on the tab
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SystemNotificationController.SearchNewAdminUsingThreeFour(System.String)">
            <summary>
            Searches the ePro Admins to return to UI
            </summary>
            <param name="searchUserName"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SystemNotificationController.SearchNewAdminUsingCOID(System.String,System.String)">
            <summary>
            Searches for admins using COID and searches by 3-4 and name
            </summary>
            <param name="searchString"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SystemNotificationController.UpdateAdminsWithAuthorization(System.Collections.Generic.List{eProcurementServices.Domain.DTO.SystemNotificationAdminDTO})">
            <summary>
            Saves the new list of Admins with authority.
            </summary>
            <param name="systemNotificationAdmins"></param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.VendorController">
            <summary>
            This controller is for methods related to vendors displayed in application
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.VendorController.GetVendorsForTypeahead(System.String,System.String,System.String)">
            <summary>
            Get a simple list of vendors for typeahead usage
            </summary>
            <param name="SearchTerm"></param>
            <param name="COID"></param>
            <param name="countryCode"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.VendorController.GetPunchOutVendors(eProcurementServices.Domain.Model.Profile.Facility)">
            <summary>
            Get list of punch-out vendors for a COID
            </summary>
            <param name="facility"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.VendorController.GetVendorDetailsById(System.String,System.Int32)">
            <summary>
            Returns the details of a vendor by Id
            </summary>
            <param name="COID"></param>
            <param name="vendorId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.VendorController.GetAllVendors(System.String)">
            <summary>
            returns header information for all vendors for a COID
            </summary>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.ItemController">
            <summary>
            This controller is for methods related to items displayed in application
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetItemByItemId(System.String,System.String)">
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetItemsForContractReportTypeahead(System.String,System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="searchTerm"></param>
            <param name="COID"></param>
            <param name="pageSize"></param>
            <param name="startAtRecord"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetItemsForIDTypeahead(System.String,System.String,System.String,System.Boolean)">
            <summary>
            Get a simple list of items for ID typeahead usage (Exact matches only)
            </summary>
            <param name="searchTerm"></param>
            <param name="COID"></param>
            <param name="countryCode"></param>
            <param name="isVendorUser"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.VboGetItemsForIDTypeahead(System.String,System.String,System.String,System.String)">
            <summary>
            Get a simple list of items for ID typeahead usage for vbo only
            </summary>
            <param name="searchTerm"></param>
            <param name="COID"></param>
            <param name="pageSize"></param>
            <param name="startAtRecord"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetItemByReorderNumber(System.String,System.String,System.Int32,System.String)">
            <summary>
            Get an item from SMART by reorder number
            </summary>
            <param name="reorderNumber"></param>
            <param name="COID"></param>
            <param name="department"></param>
            <param name="countryCode"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.Search(eProcurementServices.Domain.Model.Item.ItemSearchCriteria)">
            <summary>
            Search for items by the specified criteria
            </summary>
            <param name="itemSearchCriteria"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetAvailableParItems(System.String,System.Int32,System.String,System.String)">
            <summary>
            Get list of PARs available for an item
            </summary>
            <param name="cOID"></param>
            <param name="departmentId"></param>
            <param name="parId"></param>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetRushDeliveryMethods">
            <summary>
            Get available delivery methods
            </summary>
            <returns></returns>        
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetSPRTypes(System.String)">
            <summary>
            Get all available SPR Types
            </summary>
            <param name="cOID"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetAllUoms(System.String)">
            <summary>
            Get available Units of Measure
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetUomsForItem(System.String,System.String,System.Int32)">
            <summary>
            Get Available Units of Measure for a specific Item
            </summary>
            <param name="coid"></param>
            <param name="countryCode"></param>
            <param name="itemNumber"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetLastOrderDetails(System.String,System.String,System.String,System.String)">
            <summary>
            Get the time, date, and quantity for the last time this item was ordered on the PAR
            </summary>
            <param name="coid"></param>
            <param name="dept"></param>
            <param name="parClass"></param>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ItemController.GetItemPrice(System.String,System.String,System.String)">
            <summary>
            Get Item Price  details for the vendor Id or re-order number
            </summary>
            <param name="coid"> Facility number</param>
            <param name="reordernumber">Re-order| Vendor part number</param>
            <param name="vendornumber">Vendor Id</param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.MenuController">
            <summary>
            This controller is for methods related to menu items displayed in the application
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.MenuController.GetMenuItems(System.String,System.String)">
            <summary>
            Returns a list of menu items to show in the eProcurement application, based upon user context
            </summary>
            <param name="domain">User Domain</param>
            <param name="userName">User Username</param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.RequisitionController">
            <summary>
            This controller is for methods related to processing eProcurement requisitions
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetMyRequisitionsResults(eProcurementServices.Domain.DTO.RequisitionListMultiRequestDto)">
            <summary>
            Retrieves active requisitions and/or templates on the MyRequisitions page for the user
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetMyApprovalsResults(eProcurementServices.Domain.DTO.RequisitionListMultiRequestDto)">
            <summary>
            Retrieves pending approvals and/or approval history on the MyApprovals page for the user
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitions(eProcurementServices.Domain.DTO.DashboardRequestDTO)">
            <summary>
            Get a list of requisitions for a COID and given date range
            </summary>
            <param name="dashboardRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsByVendor(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Retrieves requisition matching Vendor Id or Vendor Name
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsByVendorReportExport(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for a COID and given Vendor Id or Vendor Name
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsForReport(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for a COID and given date range
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetVBORequisitionsForReport(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a list of Vendor Bill Only requisitions for a COID and given date range
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsForReportExport(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for a COID and given date range
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetVBORequisitionsForReportExport(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a list of Vendor Bill Only requisitions for a COID and given date range for export
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsForReportByItemNumber(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a paged list of requisitions for an item/catalog/reorder number and COID
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsForReportByItemNumberExport(eProcurementServices.Domain.DTO.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for an item/catalog/reorder number and COID
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetUpcomingApprovalsForApprover(eProcurementServices.Domain.DTO.RequisitionListRequestDto)">
            <summary>
            Get a paginated list of upcoming approvals for an approver's Upcoming Approvals widget.
            </summary>
            <param name="request"></param>
            <returns>A list of Approval objects which contain requisition details</returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetDelegateInfoForApprover">
            <summary>
            Get an approver's current delegate info
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionWorkflow(System.Int32)">
            <summary>
            Returns a combination view of history and future steps of workflow
            </summary>
            <param name="requisitionId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisition(System.Int32)">
            <summary>
            Returns a Requisition with the provided Requisition ID
            </summary>
            <param name="requisitionId">Requisition ID</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetAcquisitionEquipment">
            <summary>
            Get Available Acquisition and Equipment for a specific Item
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetParticipatingFacilities(System.String)">
            <summary>
            Get Available ParticipatingFacilities for a specific Item
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionAsApprover(System.Int32)">
            <summary>
            Returns a Requisition with the provided Requisition ID
            </summary>
            <param name="requisitionId">Requisition ID</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.CreateRequisitionItem(System.Int32,eProcurementServices.Domain.Enums.RequisitionType,System.String,System.String,System.String,System.Boolean,System.String)">
            <summary>
            Creates a new Requisition Item object for the specified item and requisition
            </summary>
            <param name="requisitionId">Requisition ID</param>
            <param name="requisitionType">Requisition Type</param>
            <param name="cOID">COID</param>
            <param name="departmentId">Department ID</param>
            <param name="itemId">Item ID</param>
            <param name="isVendorUser">True if user is a vendor</param>
            <param name="parId">Par ID To Assign (if available)</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.ValidateRequisitionItem(eProcurementServices.Domain.DTO.ValidationItem)">
            <summary>
            Validates a requisitionItem
            </summary>
            <param name="validationItem">RequisitionItem object</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.SaveRequisition(eProcurementServices.Domain.Model.Requisition.Requisition)">
            <summary>
            Saves the requisition in it's current state
            </summary>
            <param name="requisition">Requisition object</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.SaveRequisitionAsApprover(eProcurementServices.Domain.Model.Requisition.Requisition)">
            <summary>
            Saves the requisition according to changes the Approver has made.
            </summary>
            <param name="requisition"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.ConvertRequisitionToTemplate(eProcurementServices.Domain.Model.Requisition.Requisition)">
            <summary>
            Copies the passed in requisition to create a new template with the same data (also saves requisition if editable)
            </summary>
            <param name="requisition">Requisition object</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.CreateRequisitionFromTemplate(eProcurementServices.Domain.Model.Requisition.Requisition)">
            <summary>
            Copies the passed in template to create a new requisition with the same data (also saves template if editable)
            </summary>
            <param name="template">Requisition object</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.SubmitRequisition(eProcurementServices.Domain.Model.Requisition.Requisition)">
            <summary>
            Submits the requisition, requisition will be saved.
            </summary>
            <param name="requisition">Requisition object</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.UpdateRequisitionStatus(eProcurementServices.Domain.Model.Requisition.RequisitionStatusDTO)">
            <summary>
            Submits the requisition to the parent system
            </summary>
            <param name="requisitionStatusDTO"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionHistory(System.Int32)">
            <summary>
            Gets the status history of a requisition
            </summary>
            <param name="requisitionId"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetParItems(System.Int32,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Get a list of PAR items
            </summary>
            <param name="requisitionId"></param>
            <param name="cOID"></param>
            <param name="departmentId"></param>
            <param name="parId"></param>
            <param name="isVendorUser"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.PostFileAttachment">
            <summary>
            Post file attachment. Supports only 1 Attachment currently.
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetInFlightQuantity(System.String,System.String,System.String,System.String)">
            <summary>
            Get the In-Flight Qty (QOO) for an item 
            </summary>
            <param name="coid"></param>
            <param name="dept"></param>
            <param name="parClass"></param>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionsForPurchasingReport(eProcurementServices.Domain.DTO.PurchasingRequisitionReportParameters)">
             <summary>
             Retrieves a queue of purchasing requisitions for the purchasing report.
             </summary>
             <remarks>
             This endpoint retrieves paginated and filtered purchasing requisitions for generating a report. 
             It accepts a JSON object in the request body with the following parameters:
             
             **Required Parameters:**
             
             * **`Coids`**: (List of string) 
                - The list of COIDs to filter by.
                - Enter comma-separated values without quotes (e.g., `09,332`).
                - Example: `"Coids": ["123", "321"]`
            
             **Optional Parameters:**
             
             * **`advancedFilters`**: (object) 
                - An object containing advanced filter options.
                  * **`ReqTypes`**: (List of int) 
                      - The list of requisition type IDs to filter by.
                      - Enter comma-separated IDs without quotes (e.g., `1,2,3`).
                  * **`Vendors`**: (List of int) 
                      - The list of vendor IDs to filter by.
                      - Enter comma-separated IDs without quotes (e.g., `101,205`).
                  * **`Buyers`**: (List of string) 
                      - The list of buyer names to filter by.
                      - Enter comma-separated names without quotes (e.g., `John Doe, Jane Smith`).
                  * **`FilterText`**: (string) 
                      - A string value to search certain columns by.
                      - Searchable columns include: User First and Last Name, Requisition Status Description, Requisition Submission Type Description, Comments, PO Number, Parent System ID, Original Parent System ID, Location Identifier, and PAR Identifier.
                  * **`startDate`**: (Date)
                      - The starting date for filtering requisitions by creation date.
                      - If not provided, defaults to the minimum date allowed by the database.
                  * **`endDate`**: (Date)
                      - The ending date for filtering requisitions by creation date.
                      - If not provided, defaults to the maximum date allowed by the database.
             * **`pageNumber`**: (int, defaults to 1) 
                - The page number for pagination.
             * **`pageSize`**: (int, defaults to 25) 
                - The number of records to return per page.
             * **`sortColumn`**: (string, defaults to 'RequisitionId') 
                - The column to sort by.
                - Valid values are: `RequisitionId`, `LocationIdentifier`, `VendorNumber`, `VendorName`, `Date`, `RequisitionTypeId`, `FileAttachmentItemId`.
             * **`sortType`**: (string, defaults to 'ASC') 
                - The sort direction.
                - Valid values are: `ASC` (Ascending), `DESC` (Descending).
             </remarks>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetAdvancedFiltersForPurchasingReport(eProcurementServices.Domain.DTO.RequisitionPurchasingAdvancedFilterRequest)">
            <summary>
            Get a list of values for Purchasing Advanced Filter for a list of COIDs
            </summary>
            <param name="filterList"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.RequisitionController.GetRequisitionAndItemsByPONumber(System.Int32,System.String)">
            <summary>
            Handles an HTTP GET request to retrieve a list of requisitions based on the provided purchase order number and COID.
            The requisitions are returned in a JSON format.
            </summary>
            <param name="poNumber">The purchase order number.</param>
            <param name="coid">The facility ID.</param>
            <returns>A list of <see cref="T:eProcurementServices.Domain.Model.Requisition.Requisition"/> objects that match the specified criteria.</returns>
        </member>
        <member name="T:eProcurementServices.Controllers.ProfileController">
            <summary>
            This controller is for user profile information retrieval
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetGLAccount(System.String,System.Int64)">
            <summary>
            Get a specific GL Account by account number
            </summary>
            <param name="coid"></param>
            <param name="accountNumber"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetAllGLAccounts(System.String,System.String,System.String)">
            <summary>
            Get a list of All GLAccounts for the COID
            </summary>
            <param name="coid"></param>
            <param name="accountStringPartial"></param>
            <param name="countryCode"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetAzureGlAccountsSearch(System.String,System.String,System.String)">
            https://docs.microsoft.com/en-us/rest/api/searchservice/search-documents
            <summary>
            Get a typeahead list of GL Accounts for the COID
            </summary>
            <param name="coid"></param>
            <param name="accountStringPartial"></param>
            <param name="countryCode"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetAllCostCodes(System.String)">
            <summary>
            Get a list of all cost codes for a facility
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetAddresses(System.String)">
            <summary>
            Get a list of addresses for a facility
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFacilities">
            <summary>
            Get a list of facilities for the user
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetAllDepartments(System.String)">
            <summary>
            Get a list of all departments given a facility (COID)
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFacility(System.String)">
            <summary>
            Get a facility given a COID
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFavoriteFacility">
            <summary>
            Get the favorited Facility for a user
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.SetFavoriteFacility(System.String)">
            <summary>
            Sets the new favorited COID for a user
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.DeleteFavoriteFacility">
            <summary>
            Removes favorited Facility for a user
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFavoriteDepartmentId(System.String)">
            <summary>
            Get the ID of a favorited department, if one exists, given a facility (COID)
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFavoriteDepartment(System.String)">
            <summary>
            Get the user's favorite Department, if one exists, given a facility (COID)
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.SetFavoriteDepartment(eProcurementServices.Domain.DTO.PersonalizationDTO)">
            <summary>
            Sets a user's favorite department.
            </summary>
            <param name="personalization"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.DeleteFavoriteDepartment(System.String)">
            <summary>
            Removes a user's favorite department for the given facility.
            </summary>
            <param name="coid"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetVendorAffils">
            <summary>
            Get a list of the user's vendor affiliates if they are a vendor user
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetPars(System.String,System.String)">
            <summary>
            Get a list of PARs for the user/COID
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFavoriteParId(System.String,System.String)">
            <summary>
            Get the ID of a favorited PAR, if one exists, given a facility (COID) and department
            </summary>
            <param name="coid"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.GetFavoritePar(System.String,System.String)">
            <summary>
            Get the favorite Par Class, if one exists, given a facility (COID) and department
            </summary>
            <param name="coid"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.SetFavoritePar(eProcurementServices.Domain.DTO.PersonalizationDTO)">
            <summary>
            Sets a user's favorite PAR.
            </summary>
            <param name="personalization"></param>
        </member>
        <member name="M:eProcurementServices.Controllers.ProfileController.DeleteFavoritePar(System.String,System.Int32)">
            <summary>
            Removes a user's favorite PAR for the given facility and department.
            </summary>
            <param name="coid"></param>
            <param name="departmentId"></param>
        </member>
        <member name="T:eProcurementServices.Controllers.ReportController">
            <summary>
            Controller for reports
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.ReportController.GetDashboard(eProcurementServices.Domain.DTO.DashboardRequestDTO)">
            <summary>
            Get dashboard object for dashboard
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ReportController.GetUserAccessReport(System.String)">
            <summary>
            User Access Report
            </summary>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.ReportController.GetUserWorkflowsForExport(eProcurementServices.Domain.DTO.WorkflowExportInputDTO)">
            <summary>
            Gets the workflows for the users needed to create and export the .csv file for User Report
            </summary>
            <param name="workflowExportInputDTO"></param>
            <returns></returns>
        </member>
        <member name="T:eProcurementServices.Controllers.SecurityController">
            <summary>
            Controller for access information for application
            </summary>
        </member>
        <member name="M:eProcurementServices.Controllers.SecurityController.LogError(eProcurementServices.Domain.DTO.ErrorDTO)">
            <summary>
            
            </summary>
            <param name="error"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SecurityController.GetUser(System.String,System.String)">
            <summary>
            This is for pulling back user profile information
            </summary>
            <param name="domain">User domain</param>
            <param name="userName">User username</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SecurityController.GetUserParts(System.String,System.String)">
            <summary>
            This is for pulling back a list of user parts
            </summary>
            <param name="domain">User domain</param>
            <param name="userName">User username</param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SecurityController.LegacyPostUpdateCreateProcurementUser">
            <summary>
            This method replaces GetToken below for users
            that authenticate to Procurement with PING.
            PLEASE DO NOT use this call outside of Procurement
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Controllers.SecurityController.GetUserVPROBadgeInDetails(eProcurementServices.Domain.Model.Profile.VPROBadgeInRequest)">
            <summary>
            Method which will check VPRO User is Badged In with their user name
            by calling security and checking with their userName
            </summary>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Services.SecurityService.DecodeToken(System.String)">
            <summary>
            Decodes Ping token and returns user domain and username
            </summary>
            <param name="token"></param>
            <returns></returns>
            <exception cref="T:System.UnauthorizedAccessException"></exception>
        </member>
        <member name="M:eProcurementServices.Services.SecurityService.ValidateToken(System.String,System.Threading.CancellationToken)">
            <summary>
            Validates token using cached JWKS from oidcConfigManager
            </summary>
            <param name="token">Json Web Token</param>
            <param name="cancel"></param>
            <returns></returns>
        </member>
        <member name="M:eProcurementServices.Filters.TokenAuth.ChallengeAsync(System.Web.Http.Filters.HttpAuthenticationChallengeContext,System.Threading.CancellationToken)">
            <summary>
            IAuthenticationFilter implementation
            </summary>
            <param name="context"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="P:eProcurementServices.Filters.TokenAuth.AllowMultiple">
            <summary>
            IFilter implementation
            </summary>
        </member>
        <member name="T:eProcurementServices.Filters.UnhandledExceptionFilterAttribute">
            <summary>
            Represents the an attribute that provides a filter for unhandled exceptions.
            </summary>
        </member>
        <member name="M:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:eProcurementServices.Filters.UnhandledExceptionFilterAttribute"/> class.
            </summary>
        </member>
        <member name="F:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.DefaultHandler">
            <summary>
            Gets a delegate method that returns an <see cref="T:System.Net.Http.HttpResponseMessage"/> 
            that describes the supplied exception.
            </summary>
            <value>
            A <see cref="T:System.Func`3"/> delegate method that returns 
            an <see cref="T:System.Net.Http.HttpResponseMessage"/> that describes the supplied exception.
            </value>
        </member>
        <member name="F:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.GetContentOf">
            <summary>
            Gets a delegate method that extracts information from the specified exception.
            </summary>
            <value>
            A <see cref="T:System.Func`2"/> delegate method that extracts information 
            from the specified exception.
            </value>
        </member>
        <member name="P:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.Handlers">
            <summary>
            Gets the exception handlers registered with this filter.
            </summary>
            <value>
            A <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2"/> collection that contains 
            the exception handlers registered with this filter.
            </value>
        </member>
        <member name="M:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.OnException(System.Web.Http.Filters.HttpActionExecutedContext)">
            <summary>
            Raises the exception event.
            </summary>
            <param name="actionExecutedContext">The context for the action.</param>
        </member>
        <member name="M:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.Register``1(System.Net.HttpStatusCode)">
            <summary>
            Registers an exception handler that returns the specified status code for exceptions of type <typeparamref name="TException"/>.
            </summary>
            <typeparam name="TException">The type of exception to register a handler for.</typeparam>
            <param name="statusCode">The HTTP status code to return for exceptions of type <typeparamref name="TException"/>.</param>
            <returns>
            This <see cref="T:eProcurementServices.Filters.UnhandledExceptionFilterAttribute"/> after the exception handler has been added.
            </returns>
        </member>
        <member name="M:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.Register``1(System.Func{System.Exception,System.Net.Http.HttpRequestMessage,System.Net.Http.HttpResponseMessage})">
            <summary>
            Registers the specified exception <paramref name="handler"/> for exceptions of type <typeparamref name="TException"/>.
            </summary>
            <typeparam name="TException">The type of exception to register the <paramref name="handler"/> for.</typeparam>
            <param name="handler">The exception handler responsible for exceptions of type <typeparamref name="TException"/>.</param>
            <returns>
            This <see cref="T:eProcurementServices.Filters.UnhandledExceptionFilterAttribute"/> after the exception <paramref name="handler"/> 
            has been added.
            </returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="handler"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:eProcurementServices.Filters.UnhandledExceptionFilterAttribute.Unregister``1">
            <summary>
            Unregisters the exception handler for exceptions of type <typeparamref name="TException"/>.
            </summary>
            <typeparam name="TException">The type of exception to unregister handlers for.</typeparam>
            <returns>
            This <see cref="T:eProcurementServices.Filters.UnhandledExceptionFilterAttribute"/> after the exception handler 
            for exceptions of type <typeparamref name="TException"/> has been removed.
            </returns>
        </member>
    </members>
</doc>
