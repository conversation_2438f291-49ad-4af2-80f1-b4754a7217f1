﻿<div class="item-list-container" id="item-list-scroll">
    <ul class="list-item-maxwidth" style="list-style-type: none; padding: 0;">
        <li ng-if="!item.IsHidden" class="cardResult" scroll-to-bottom requisition-type="{{ requisition.RequisitionType }}" requisition-enum="{{ reqTypesEnum }}" ng-repeat="item in itemList | filter: filterFunction track by $index" ng-class="{componentItem: item.ParItem.IsSubItem || item.ParentRequisitionItemId}">
            <div class="cardResultHeader" style="position:relative">
                <span ng-if="requisition.RequisitionStatusType != reqStatusTypesEnum.SUBMITTED.Id && requisition.RequisitionStatusType != reqStatusTypesEnum.LEGACY.Id && requisition.RequisitionStatusType != reqStatusTypesEnum.SUBMISSIONERROR.Id" class="smallBorderBottom {{item.RequisitionItemStatusType | requisitionItemStatusClass}}">
                    <span class="{{item.RequisitionItemStatusType | requisitionItemStatusIcon}}"></span>
                    <span ng-show="item.IsInvalid && !item.Warning" ng-bind-html="item.RequisitionItemStatus"></span>
                    <span ng-show="item.IsInvalid && item.Warning && !item.SPRDetail" ng-bind-html="item.RequisitionItemStatus + ': ' + item.Warning"></span>
                </span>
                <span ng-if="!readOnly">
                    <span ng-if="requisition.RequisitionStatusType == reqStatusTypesEnum.SUBMITTED.Id || requisition.RequisitionStatusType == reqStatusTypesEnum.LEGACY.Id">
                        <span class="smallBorderBottom {{item.RequisitionItemStatusType | requisitionItemStatusClass}}" ng-bind="item.RequisitionItemStatus"></span>
                    </span>
                </span>
                <span ng-if="readOnly">
                    <span ng-if="requisition.RequisitionStatusType == reqStatusTypesEnum.SUBMITTED.Id || requisition.RequisitionStatusType == reqStatusTypesEnum.LEGACY.Id">
                        <span class="smallBorderBottom {{item.RequisitionItemStatusType | requisitionItemStatusClass}}" ng-bind="item.RequisitionItemStatus"></span>
                        <span class="smallBorderBottom {{item.RequisitionItemStatusType | requisitionItemStatusClass}}" ng-if="item.RequisitionItemStatusType == reqItemStatusTypesEnum.SCHEDULED.Id && item.RequisitionScheduledDate != null">({{item.RequisitionScheduledDate | date: 'short'}})</span>
                        <span class="glyphicon glyphicon-info-sign" style="color: black" ng-if="item.RequisitionItemStatusType == reqItemStatusTypesEnum.DENIED.Id && item.SPRDetail != null" tooltip="{{RemoveSpacesForDeniedItem(item.SPRDetail.RejectionComments)}}" tooltip-append-to-body="true" tooltip-placement="right"></span>
                        <span class="glyphicon glyphicon-info-sign" style="color: black" ng-if="item.RequisitionItemStatusType == reqItemStatusTypesEnum.DENIED.Id && item.SPRDetail == null" tooltip="{{RemoveSpacesForDeniedItem(item.RejectionComments)}}" tooltip-append-to-body="true" tooltip-placement="right"></span>
                    </span>
                </span>
                <span ng-if="!readOnly">
                    &nbsp;
                    <span class="position-right paddingRight15">
                        <a tabindex="-1" href ng-click="removeItem(item)" ng-if="!dsoView" class="text-danger noUnderline glyphicon glyphicon-remove"></a>
                    </span>
                </span>
            </div>
            <div class="row paddingRight15">
                <div class="col-xs-12 col-sm-6 col-print-6">
                    <div class="text-overflow-ellipsis-multiline">
                        <div ng-if="item.VboHoldItemConversion != null">
                            <a tabindex="-1" class="requisition-item-details-id-and-description" href ng-click="openItemDetailsModal(requisition.Facility, item.displayItemId)">
                                <span ng-bind="item.displayItemId" class="requisition-item-details-id"></span> - <span ng-bind="item.VboHoldItemConversion.ItemDetails.Item.Description" class="requisition-item-details-description uppercase"></span>
                            </a>
                        </div>
                        <div ng-if="item.VboHoldItemConversion == null">
                            <a tabindex="-1" ng-if="((item.IsFileItem && item.displayItemId != item.SPRDetail.PartNumber) || (item.ParItem != null && item.displayItemId)) && !item.IsWastePar" class="requisition-item-details-id-and-description" href ng-click="openItemDetailsModal(requisition.Facility, item.displayItemId)">
                                <span ng-bind="item.displayItemId" class="requisition-item-details-id"></span> - <span ng-bind="item.Item.Description != null ? item.Item.Description : item.SPRDetail.ItemDescription" class="requisition-item-details-description uppercase"></span>
                            </a>
                            <span ng-if="((!item.IsFileItem && item.ParItem == null) || (item.IsFileItem && item.displayItemId == item.SPRDetail.PartNumber)) && !item.IsWastePar" class="requisition-item-details-id-and-description">
                                <span ng-bind="item.displayItemId" class="requisition-item-details-id"></span> - <span ng-bind="item.Item.Description != null ? item.Item.Description : item.SPRDetail.ItemDescription" class="requisition-item-details-description uppercase"></span>
                            </span>
                            <span ng-if="item.IsWastePar" class="requisition-item-details-id-and-description">
                                <span ng-bind="getWasteItemId(item)" class="requisition-item-details-id"></span> - <span ng-bind="item.Item.Description != null ? item.Item.Description : item.SPRDetail.ItemDescription" class="requisition-item-details-description uppercase"></span>
                            </span>
                            <span ng-if="!item.IsWastePar && !item.displayItemId && item.SPRDetail" class="requisition-item-details-id-and-description">
                                <span ng-bind="item.SPRDetail.PartNumber" class="requisition-item-details-id"></span> - <span ng-bind="item.Item.Description != null ? item.Item.Description : item.SPRDetail.ItemDescription" class="requisition-item-details-description uppercase"></span>
                            </span>
                        </div>
                    </div>
                    <div class="text-overflow-ellipsis-multiline">
                        <div ng-if="item.VboHoldItemConversion != null">
                            <span>Vendor:</span>
                            <span ng-bind="item.displayVendorName" class="requisition-item-details-vendor-name"></span>
                            <a tabindex="-1" href ng-click="openVendorDetailsModal(requisition.Facility, item.VboHoldItemConversion.ItemDetails.Item.Vendor.Id)" class="requisition-item-details-vendor-id">
                                ({{item.VboHoldItemConversion.ItemDetails.Item.Vendor.Id}})
                            </a>
                        </div>
                        <div ng-if="item.VboHoldItemConversion == null">
                            <span>Vendor:</span>
                            <span ng-if="item.SPRDetail.Vendor" ng-bind="item.displayVendorName" class="requisition-item-details-vendor-name"></span>
                            <a tabindex="-1" href ng-if="item.SPRDetail.Vendor && item.SPRDetail.Vendor.Id" ng-click="openVendorDetailsModal(requisition.Facility, item.SPRDetail.Vendor.Id)" class="requisition-item-details-vendor-id">
                                ({{item.SPRDetail.Vendor.Id}})
                            </a>
                            <span ng-if="!item.SPRDetail.Vendor" ng-bind="item.displayVendorName" class="requisition-item-details-vendor-name"></span>
                            <a tabindex="-1" href ng-if="!item.SPRDetail.Vendor && item.Item.Vendor.Id" ng-click="openVendorDetailsModal(requisition.Facility, item.Item.Vendor.Id)" class="requisition-item-details-vendor-id">
                                ({{item.Item.Vendor.Id}})
                            </a>
                        </div>
                    </div>
                    <div>
                        <div ng-if="item.VboHoldItemConversion != null">
                            <span>Reorder #:</span>
                            <span ng-bind="item.VboHoldItemConversion.ItemDetails.Item.ReorderNumber" class="requisition-item-details-reorder-number"></span>
                        </div>
                        <div ng-if="item.VboHoldItemConversion == null">
                            <span>Reorder #:</span>
                            <span ng-if="!item.IsSPR && !requisition.isCapitalReq" ng-bind="item.Item.ReorderNumber" class="requisition-item-details-reorder-number"></span>
                            <span ng-if="item.IsSPR || requisition.isCapitalReq" ng-bind="item.SPRDetail.PartNumber" class="requisition-item-details-spr-reorder-number"></span>
                        </div>
                    </div>
                    <div ng-if="requisition.RequisitionType != ReqTypeDescriptionEnum.CAPITAL.Id && requisition.RequisitionType != ReqTypeDescriptionEnum.PUNCHOUT.Id">
                        <div ng-if="item.VboHoldItemConversion != null">
                            <span>Catalog #:</span>
                            <span ng-bind="item.VboHoldItemConversion.ItemDetails.Item.ManufacturerCatalogNumber" class="requisition-item-details-item-catalog-number"></span>
                        </div>
                        <div ng-if="item.VboHoldItemConversion == null">
                            <span>Catalog #:</span>
                            <span ng-if="(item.Item && item.IsFileItem && item.SmartItemNumber && item.Item.ManufacturerCatalogNumber) || item.ParItem || isLegacy" ng-bind="item.Item.ManufacturerCatalogNumber" class="requisition-item-details-item-catalog-number"></span>
                            <span ng-if="!(item.Item && item.IsFileItem && item.SmartItemNumber && item.Item.ManufacturerCatalogNumber) && !item.ParItem && !isLegacy" class="requisition-item-details-item-catalog-number">N/A</span>
                        </div>
                    </div>
                    <div>
                        <div ng-if="dsoView && !isVboRequisition">
                            <div>
                                Procedure Code:
                                <span id="ProcedureCode" ng-bind="getProcedureCode(item)"></span>
                            </div>
                            <div>
                                Chargeable:
                                <span id="Chargeable" ng-bind="isChargeable(item)"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-8 col-sm-4 col-print-4 paddingLeft0 paddingRight0" ng-if="!dsoButtonClicked">
                    <div class="col-xs-12 col-sm-6 col-print-6 col-sm-padding-left-0" >
                        <div>
                            <span>PAR:</span>
                            <span ng-if="item.VboHoldItemConversion != null">
                                <strong ng-bind="item.displayParId" class="requisition-item-details-par-id"></strong>
                            </span>
                            <span ng-if="item.VboHoldItemConversion == null">
                                <span ng-if="!readOnly">
                                    <span ng-if="!item.IsSPR && !requisition.isCapitalReq && item.AvailableParItems.length" class="dropdown" dropdown keyboard-nav>
                                        <button tabindex="-1" type="button" class="btn dropdown-toggle btnPlainDropDown" style="width:60px" dropdown-toggle aria-expanded="false">
                                            <span class="inlineBlock pull-left" ng-hide="item.AvailableParItems.length">N/A</span>
                                            <span class="inlineBlock pull-left requisition-item-details-par-id" ng-bind="item.displayParId"></span>
                                            <span class="inlineBlock pull-right"><span class="caret"></span></span>
                                        </button>
                                        <ul class="dropdown-menu hideWhenPrint" role="menu" ng-show="item.AvailableParItems.length">
                                            <li role="menuitem" ng-repeat="parItem in item.AvailableParItems">
                                                <a href ng-click="parEdited(item, parItem, requisition.RequisitionItems.indexOf(item));">
                                                    <span ng-bind="parItem.ParDescription"></span> (<span ng-bind="parItem.ParId"></span>)
                                                    <span class="{{parItem.ParType | parTypeIcon}}" tooltip="{{parItem.ParTypeDescription}}" tooltip-append-to-body="true"></span>
                                                </a>
                                            </li>
                                        </ul>
                                    </span>
                                    <strong ng-if="item.IsSPR || requisition.isCapitalReq || !item.AvailableParItems.length">N/A</strong>
                                </span>
                                <span ng-if="readOnly">
                                    <strong ng-bind="item.displayParId" class="requisition-item-details-par-id"></strong>
                                </span>
                            </span>
                        </div>
                        <div>
                            <span>Location:</span>
                            <span>
                                <strong ng-bind="item.displayParItemLocation" class="requisition-item-details-location"></strong>
                            </span>
                        </div>
                        <div>
                            <span>Stock:</span>
                            <span>
                                <strong ng-bind="item.displayIsStock | yesNo" class="stock requisition-item-details-stock-indicator"></strong>
                            </span>
                        </div>
                        <div ng-if="item.SPRDetail.BudgetNumber && item.VboHoldItemConversion == null">
                            <span>Project #:</span>
                            <strong ng-bind="item.SPRDetail.BudgetNumber" class="requisition-item-details-project-number"></strong>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-6 col-print-6 col-sm-padding-left-0">
                        <div ng-if="hasPriceViewerRole">
                            <div>
                                <span class="tooltipIndicator" tooltip="Unit Cost" tooltip-append-to-body="true">Unit:</span>
                                <span ng-if="item.VboHoldItemConversion != null">
                                    <strong ng-if="item.Discount" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.VboHoldItemConversion.ItemDetails.ParPrice * (100 - item.Discount)/100 | currency: requisition.facilityCurrency"></strong>
                                    <strong ng-if="!item.Discount" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.VboHoldItemConversion.ItemDetails.ParPrice | currency: requisition.facilityCurrency"></strong>
                                </span>
                                <span ng-if="item.VboHoldItemConversion == null">
                                    <strong ng-if="!item.IsSPR && !requisition.isCapitalReq && !requisition.IsRequisitionEditable && !item.Discount" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.UnitCost || item.Item.Price | currency: requisition.facilityCurrency"></strong>
                                    <strong ng-if="!item.IsSPR && !requisition.isCapitalReq && !requisition.IsRequisitionEditable && item.ParItem && item.Discount && !requisition.IsVendor" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.ParItem.ParPrice * (100 - item.Discount)/100 | currency: requisition.facilityCurrency"></strong>
                                    <strong ng-if="!item.IsSPR && !requisition.isCapitalReq && requisition.IsRequisitionEditable && item.ParItem && item.Discount && !requisition.IsVendor" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.ParItem.ParPrice * (100 - item.Discount)/100 | currency: requisition.facilityCurrency"></strong>
                                    <strong ng-if="!item.IsSPR && !requisition.isCapitalReq && requisition.IsRequisitionEditable && item.ParItem && !item.Discount" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.ParItem.ParPrice | currency: requisition.facilityCurrency"></strong>
                                    <strong ng-if="!item.IsSPR && !requisition.isCapitalReq && requisition.IsRequisitionEditable && !item.ParItem && !item.Discount" class="UnitCost requisition-item-details-unit-cost" ng-bind="item.Item.Price | currency: requisition.facilityCurrency"></strong>
                                        <span ng-if="item.IsSPR && !isBillOnlyReview && (approvalView && canEdit())">
                                            <strong class="currency-symbol"> {{requisition.facilityCurrency}}</strong>
                                            <input ng-if="item.IsSPR && (requisition.IsVendor || isVendorUser)" placeholder="Price" type="number" step="0.01" ng-maxlength="12" min="0.000" max="99999999.999" ng-model="item.SPRDetail.EstimatedPrice" ng-change="priceEdited(item)" ng-class="{'label-edited': item.SPRDetail.HasApproverChangedEstimatedPrice}" class="form-control input-sm requisition-item-details-spr-unit-cost-input" style="min-width: 50px; max-width: 80px; display: inline-block; padding-left: 14px;" />
                                        </span>
                                    <span ng-if="!approvalView || !canEdit() || isBillOnlyReview">
                                        <strong ng-if="(item.IsSPR || requisition.isCapitalReq) && !item.Discount" class="UnitCost requisition-item-details-spr-unit-cost" ng-bind="item.SPRDetail.EstimatedPrice | currency: requisition.facilityCurrency"></strong>
                                        <strong ng-if="item.IsSPR && (requisition.IsVendor || isVendorUser) && item.Discount" class="UnitCost requisition-item-details-spr-unit-cost" ng-bind="item.SPRDetail.EstimatedPrice * (100 - item.Discount)/100 | currency: requisition.facilityCurrency"></strong>
                                    </span>
                                    <strong ng-if="!item.IsSPR && (requisition.IsVendor || isVendorUser) && item.Discount && !item.ParItem" class="UnitCost requisition-item-details-spr-unit-cost" ng-bind="item.Item.Price * (100 - item.Discount)/100 | currency: requisition.facilityCurrency"></strong>
                                    <strong ng-if="!item.IsSPR && (requisition.IsVendor || isVendorUser) && item.Discount && item.ParItem" class="UnitCost requisition-item-details-spr-unit-cost" ng-bind="item.ParItem.ParPrice * (100 - item.Discount)/100 | currency: requisition.facilityCurrency"></strong>
                                </span>
                            </div>
                            <div>
                                <span class="tooltipIndicator" tooltip="Total Cost" tooltip-append-to-body="true">Total:</span>
                                <strong class="TotalCost requisition-item-details-total-cost" ng-bind="itemTotalCost(item) | currency: requisition.facilityCurrency"></strong>
                            </div>
                        </div>
                        <div>
                            <span>GL:</span>
                            <span ng-if="item.VboHoldItemConversion != null">
                                <strong ng-bind="item.VboHoldItemConversion.ItemDetails.GLAccount" class="requisition-item-details-gl"></strong>
                            </span>
                            <span ng-if="item.VboHoldItemConversion == null">
                                <strong ng-if="!isLegacy" ng-bind="item.SPRDetail.GeneralLedgerCodeString == null ? item.ParItem.GLAccount : item.SPRDetail.GeneralLedgerCodeString" class="requisition-item-details-gl"></strong>
                                <strong ng-if="isLegacy" ng-bind="item.GLAccount ? item.GLAccount : item.ParItem.GLAccount" class="requisition-item-details-gl"></strong>
                            </span>
                        </div>
                        <div ng-if="(item.ParItem || item.Item) && requisition.RequisitionStatusType != reqItemStatusTypesEnum.PENDINGAPPROVAL && requisition.RequisitionStatusType != reqItemStatusTypesEnum.SUBMITTED && requisition.IsRequisitionEditable">
                            <span class="tooltipIndicator" tooltip="Quantity In-Stock at Distribution Point does not always reflect real-time counts">QIS:</span>
                            <strong ng-if="item.ParItem && item.ParItem.Item && item.ParItem.Item.QuantityAvailable != null" ng-bind="item.Item.IsStock ? item.ParItem.Item.QuantityAvailable : '0'" class="requisition-item-details-quantity-available"></strong>
                            <strong ng-if="item.Item && !item.ParItem && item.Item.QuantityAvailable != null" ng-bind="item.Item.IsStock ? item.Item.QuantityAvailable : '0'" class="requisition-item-details-quantity-available"></strong>
                            <strong ng-if="item.Item.QuantityAvailable == null && item.ParItem.Item.QuantityAvailable == null" class="requisition-item-details-quantity-available">N/A</strong>
                        </div>
                    </div>
                </div>
                <div class="col-xs-4 col-sm-2 col-print-2 paddingLeft10 paddingRight0">
                    <div class="col-xs-12 col-sm-6 col-print-6 paddingLeft0">
                        <span class="tooltipIndicator" tooltip="Unit of Measure" tooltip-append-to-body="true">UOM:</span>
                        <span ng-if="item.VboHoldItemConversion != null">
                            <strong ng-bind="item.VboHoldItemConversion.ItemDetails.IssueUOM" class="requisition-item-details-par-uom"></strong>
                        </span>
                        <span ng-if="item.VboHoldItemConversion == null">
                            <strong ng-if="!item.IsSPR && !requisition.isCapitalReq && !isLegacy" ng-bind="item.ParItem.IssueUOM" class="requisition-item-details-par-uom"></strong>
                            <strong ng-if="item.IsSPR || requisition.isCapitalReq && !isLegacy" ng-bind="item.SPRDetail.UOM.Code" class="requisition-item-details-spr-uom"></strong>
                            <strong ng-if="isContractVboItem(item) && !item.ParItem" ng-bind="item.Item.UOM" class="requisition-item-details-spr-uom"></strong>
                            <strong ng-if="isLegacy" ng-bind="item.UOM" class="requisition-item-details-spr-uom"></strong>
                        </span>
                        <div ng-if="!cartView && !item.IsSPR && !requisition.isCapitalReq && !isBillOnlyInfoAvailable && !isLegacy && !dsoView">
                            <div>
                                <span>Min:</span>
                                <strong ng-bind="item.ParItem.MinStock" class="requisition-item-details-min-quantity"></strong>
                            </div>
                            <div>
                                <span>Max:</span>
                                <strong ng-bind="item.ParItem.MaxQuantityToOrder" class="requisition-item-details-max-quantity"></strong>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-6 col-print-6 paddingLeft0">
                        <span class="tooltipIndicator" tooltip="Quantity to Order" tooltip-append-to-body="true">QTY:</span>
                        <div ng-if="!readOnly || (requisition.IsVendor && canEdit() && !isBillOnlyReview)">
                            <input type="submit" disabled style="display: none" aria-hidden="true" />
                            <input ng-if="!item.ParItem.IsMainItem && !dsoView" placeholder="Qty" type="number" ng-maxlength="9" min="0" ng-model="item.QuantityToOrder" ng-blur="qtyEdited(item,requisition.RequisitionItems.indexOf(item))" ng-change="item.HasQuantityToOrderChanged = true" ng-class="{'label-edited': item.HasQuantityToOrderChanged && approvalView}" class="form-control input-sm requisition-item-details-quantity" style="min-width: 50px; max-width:60px" />
                            <div ng-if="item.ParItem.IsMainItem && !dsoView" class="btnPlainSelect">
                                <select ng-model="item.QuantityToOrder" number-options ng-change="item.HasQuantityToOrderChanged = true; qtyEdited(item,requisition.RequisitionItems.indexOf(item));">
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                </select>
                            </div>
                        </div>
                        <strong ng-if="!canEdit() || dsoView || isBillOnlyReview" ng-bind="(item.QuantityToOrder | number)" class="requisition-item-details-quantity"></strong>
                        <div ng-if="readOnly && requisition.IsMobile">
                            <div>
                                <span class="tooltipIndicator" tooltip="Quantity on Hand" tooltip-append-to-body="true">QOH:</span>
                                <strong ng-bind="item.QuantityOnHand" class="requisition-item-details-quantity-on-hand"></strong>
                            </div>
                            <div>
                                <span class="tooltipIndicator" tooltip="Quantity on Order" tooltip-append-to-body="true">QOO:</span>
                                <strong ng-bind="item.QuantityInFlight" class="requisition-item-details-quantity-on-order"></strong>
                            </div>
                        </div>
                        <div ng-if="readOnly && (item.RequisitionItemStatusType == reqItemStatusTypesEnum.ITEMRECEIVED.Id || item.RequisitionItemStatusType == reqItemStatusTypesEnum.REQFILLED.Id || item.RequisitionItemStatusType == reqItemStatusTypesEnum.NOPARTIALFILL.Id) && (!item.IsSPR || isBillOnlyInfoAvailable || (item.IsSPR && item.Item.IsStock))">
                            <span>
                                <span class="tooltipIndicator" tooltip="Quantity Filled" tooltip-append-to-body="true">FILLED:</span>
                                <strong class="textOrange requisition-item-details-quantity-fulfilled" ng-bind="item.QuantityFulfilled"></strong>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cardResultFooter marginTop5 position-relative" id="reqCardFooterText">
                <span ng-if="readOnly && item.IsRushOrder">
                    <span class="rushIcon"></span> Rush
                </span>
                <span ng-if="approvalView && canEdit()">
                    <button class="btn-xs blue conversion-button" ng-click="revertHoldItemConversion(item, requisition)" ng-disabled="isConversionModalOpen" ng-if="canApproveRequisition && item.VboHoldItemConversion != null"><span class="glyphicon glyphicon-share-alt flip-text"></span> Revert to Hold item</button>
                    <button class="btn-xs orange conversion-button" ng-click="openParModal(item)" ng-disabled="isConversionModalOpen" ng-if="canApproveRequisition && item.VboHoldItemConversion == null && item.RequisitionItemStatusType == reqItemStatusTypesEnum.HOLD.Id && (requisition.RequisitionStatusType == reqStatusTypesEnum.PENDINGAPPROVAL.Id || requisition.RequisitionStatusType == reqStatusTypesEnum.ONHOLD.Id || requisition.RequisitionSubmissionTypeId == 1)"><span class="glyphicon glyphicon-refresh"></span> Convert to PAR item</button>
                </span>
                <ul class="noDots-ul" style="padding: 0">
                    <li id="autoSub-label" class="bold " ng-if="item.ParentRequisitionItemId">Auto-Sub Item</li>
                    <li ng-if="item.IsWastePar && !canEdit()" class="form-inline control-label-checkbox" style="cursor: auto !important">
                        <strong>{{item.Discount}}% Waste Discount</strong>
                    </li>
                    <li ng-if="canEditWaste(item) && !dsoView" class="form-inline control-label-checkbox">
                        <input ng-attr-id="{{'waste-checkbox-' + $index}}" class="showHand" type="checkbox" ng-change="makeWaste(item)" ng-model="item.IsWastePar" ng-disabled="approvalView" />
                        <label ng-attr-for="{{'waste-checkbox-' + $index}}" ng-class="{'showHand': !approvalView}"><small>[Waste]</small></label>
                        <input ng-attr-id="{{'discount-input-' + $index}}" type="number" min="0" max="100" step="0.01" pattern="^\d+(?:\.\d{1,2})?$" ng-class="{'label-edited': item.HasDiscountChanged}" class="form-control input-xs" style="width: 65px" ng-disabled="!item.IsWastePar" ng-change="discountEdited(item)" ng-model="item.Discount" />
                        <small>% Discount</small>
                    </li>
                    <li class="form-inline col-print-12 marginRight10" ng-repeat="lspair in item.LotSerialPairs | requisitionItemLotSerialDisplayFilter: lspair" ng-if="isBillOnlyInfoAvailable && !item.ParItem.IsMainItem">
                        <span>
                            <small ng-if="canEdit() || lspair.LotNumber">LOT #:</small>
                            <strong ng-if="!canEdit() && lspair.LotNumber" ng-bind="lspair.LotNumber" class="requisition-item-details-lot-number marginRight10"></strong>
                            <strong ng-if="!canEdit() && !lspair.LotNumber" ng-bind="lspair.LotNumber" class="requisition-item-details-lot-number"></strong>
                            <input tabindex="0" ng-if="canEdit()" placeholder="Lot #" maxlength="24" ng-model="lspair.LotNumber" ng-change="lotEdited(lspair)" input-type class="input-xs form-control requisition-item-details-lot-number"
                                   ng-class="{'highlight-red': highlightLotSerialFields(item, lspair), 'label-edited': (lspair.ChangeStatus != null && lspair.ChangeStatus != changeStatusEnum.NOCHANGE.Id) && approvalView}" />
                        </span><br />
                        <span>
                            <small ng-show="canEdit() || lspair.SerialNumber" class="noMarginPrint">SERIAL #:</small>
                            <strong ng-if="!canEdit() && item.UpchargeCost" ng-bind="lspair.SerialNumber" class="requisition-item-details-serial-number marginRight10"></strong>
                            <strong ng-if="!canEdit() && !item.UpchargeCost" ng-bind="lspair.SerialNumber" class="requisition-item-details-serial-number"></strong>
                            <input tabindex="0" ng-show="canEdit()" placeholder="Serial #" maxlength="24" ng-model="lspair.SerialNumber" ng-change="serialEdited(lspair)" input-type class="input-xs form-control requisition-item-details-serial-number"
                                   ng-class="{'highlight-red': highlightLotSerialFields(item, lspair), 'label-edited': (lspair.ChangeStatus != null && lspair.ChangeStatus != changeStatusEnum.NOCHANGE.Id) && approvalView}" />
                        </span>
                        <span class="form-inline col-print-12" ng-show="canEdit()">
                            <a tabindex="-1" href ng-click="deleteLotSerialPair(item.LotSerialPairs, lspair)" class="text-danger noUnderline glyphicon glyphicon-minus-sign"></a>
                        </span>
                        <span ng-if="$last" class="form-inline col-print-12" ng-show="canEdit() && lsPairAddButtonAvailable(item)">
                            <a tabindex="-1" href ng-click="addLotSerialPair(item)" class="textGreen noUnderline glyphicon glyphicon-plus-sign"></a>
                        </span>
                        <span class="form-inline col-print-12 top-zero" ng-if="item.ParItem.IsUpchargeAvailable && $first">
                            <span ng-if="readOnly && item.UpchargeCost" tooltip-append-to-body="true">
                                <small>UPCHARGE:</small>
                                <strong ng-bind="item.UpchargeCost | currency: requisition.facilityCurrency" class="requisition-item-details-upcharge"></strong>
                            </span>
                            <span ng-if="!readOnly">
                                <small class="noMarginPrint">UPCHARGE:</small>
                                <input tabindex="0" class="input-xs form-control requisition-item-details-upcharge" placeholder="Upcharge" type="number" ng-maxlength="12" min="0" max="99999999.999" step="0.001" ng-model="item.UpchargeCost" ng-blur="upchargeEdited(item)" />
                            </span>
                        </span>
                    </li>
                    <li ng-if="featureFlags.DigitalSignOffIsEnabled && ((requisition.RequisitionStatusType == reqStatusTypesEnum.DRAFT.Id && dsoView) && dsoButtonClicked || approvalsDsoButtonClicked)" class="control-checkbox-dso">
                        <label style="font-weight:100">Clinician Sign-Off <input type="checkbox" ng-disabled="approvalsDsoButtonClicked" ng-model="item.IsConfirmed" /></label>
                    </li>
                </ul>
                <span class="control-label-checkbox" ng-if="!readOnly && isRushReq">
                    <input tabindex="0" ng-attr-id="{{'spr-rush-item-' + $index}}" class="showHand" type="checkbox" name="sprIsRushItem" ng-model="item.IsRushOrder" />
                    <label ng-attr-for="{{'spr-rush-item-' + $index}}" class="showHand"><small>[Rush]</small></label>
                </span>
                <span class="form-inline col-print-12 top-zero" ng-if="item.ParItem.IsUpchargeAvailable && !isBillOnlyInfoAvailable">
                    <span ng-if="readOnly && item.UpchargeCost" tooltip-append-to-body="true">
                        <small>UPCHARGE:</small>
                        <strong ng-bind="item.UpchargeCost | currency: requisition.facilityCurrency" class="requisition-item-details-upcharge"></strong>
                    </span>
                </span>
                <span class="position-right paddingRight5 top-zero poNumberContainer" id="smartReqId">
                    <small ng-if="item.PONumber" class="pipe-delineated">
                        PO #: <a tabindex="0" class="tooltipIndicator requisition-item-details-po-number" tooltip="Purchase Order Details" tooltip-append-to-body="true" href ng-click="openPurchaseOrderDetailsModal(requisition.Facility, item.PONumber)" ng-bind="item.PONumber"></a>
                    </small>
                    <small ng-if="item.IsPurged" class="pipe-delineated">
                        <b>Purged</b>
                    </small>
                    <small ng-if="item.ParentSystemId" class="pipe-delineated">
                        SMART REQ #: {{item.ParentSystemId}}
                    </small>
                    <small ng-if="hasIconsOrText(item, readOnly) && !isBillOnlyReview" class="pipe-delineated">
                        <span ng-if="showFileAttachmentIcon(item)" class="attachIcon top-zero"></span>
                        <span ng-if="showSprFlagIcon(item)" class="sprIcon top-zero"></span>
                        <span ng-if="showSprFileItemHasChangedIcon(item)" class="sprFileItemHasChangedIcon top-zero" tooltip="Flagged fields deviate from the Standard File Item details" tooltip-append-to-body="true"></span>
                        <a tabindex="0" href ng-if="showApprovalRequestLink(item, readOnly)" ng-click="openApprovalRequestBlade(requisition.RequisitionItems.indexOf(item))" class="requisition-item-details-approval-request top-zero">Approval Request</a>
                        <span ng-if="isContractVboItem(item)" class="requisition-item-on-contract top-zero textCharcoalGray">Item On Contract</span>
                    </small>
                </span>
                <!-- ng-if="item.sprDetail.AcqType" -->
                <div ng-if="((featureFlags.FFIsEntryExitEnabled && (item.SPRDetail.AcquisitionType == 'Purchased' || item.SPRDetail.AcquisitionType == 'Leased')) || (featureFlags.FFIsEntryExitEnabledForStandardRequisition && item.SPRDetail.SPRType.Description == 'Entry Exit')) && !ShowAcquisitionEquipment">
                    <span class="form-inline col-print-24">
                        <small class="noMarginPrint marginRight4">Parts warranty</small>
                        <input tabindex="0" class="input-xs form-control requisition-item-details-upcharge marginRight5" placeholder="#Months" type="number" ng-maxlength="12"
                               ng-model="item.PartsWarrantyMonths" ng-init="checkForNullAndUpdate()"
                               min="0" max="99999999.999" step="1" />
                        <small class="noMarginPrint">Parts warranty End : </small>
                        <small class="noMarginPrint">{{getWarrantyMonths(item.PartsWarrantyMonths)}}</small>
                    </span>
                </div>
                <div ng-if="((featureFlags.FFIsEntryExitEnabled && (item.SPRDetail.AcquisitionType == 'Purchased' || item.SPRDetail.AcquisitionType == 'Leased')) || (featureFlags.FFIsEntryExitEnabledForStandardRequisition && item.SPRDetail.SPRType.Description == 'Entry Exit')) && !ShowAcquisitionEquipment">
                    <span class="form-inline col-print-24">

                        <small class="noMarginPrint">Labor warranty</small>
                        <input tabindex="0" class="input-xs form-control requisition-item-details-upcharge marginRight5" placeholder="#Months" type="number"
                               ng-maxlength="12" min="0" max="99999999.999" step="1" ng-model="item.LaborWarrantyMonths" />
                        <small class="noMarginPrint">Labor warranty End : </small>
                        <small class="noMarginPrint">{{getWarrantyMonths(item.LaborWarrantyMonths)}}</small>
                    </span>
                </div>
                <div ng-if="isBillOnlyReview">
                    <span class="form-inline col-print-24 top-zero">
                        <small class="noMarginPrint">Procedure Code: {{::getProcedureCode(item)}}</small>
                        <br />
                        <small class="noMarginPrint">Chargeable: {{::isChargeable(item)}}</small>
                    </span>
                </div>
            </div>
        </li>
    </ul>
</div>